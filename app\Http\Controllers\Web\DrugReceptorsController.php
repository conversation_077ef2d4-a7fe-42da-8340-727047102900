<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\DrugReceptors;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DrugReceptorsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                // Define columns for ordering
                $columns = ['id', 'receptor_name', 'receptor_description', 'created_by', 'created_at'];
                $orderBy = $columns[$orderColumn] ?? 'id';

                // Base query
                $query = DrugReceptors::with('createdBy');

                // Apply search filter
                if (!empty($searchValue)) {
                    $query->where(function ($q) use ($searchValue) {
                        $q->where('receptor_name', 'LIKE', "%{$searchValue}%")
                          ->orWhere('receptor_description', 'LIKE', "%{$searchValue}%");
                    });
                }

                // Get total count before pagination
                $totalRecords = DrugReceptors::count();
                $filteredRecords = $query->count();

                // Apply ordering and pagination
                $drugReceptors = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

                // Format data for DataTable
                $data = [];
                foreach ($drugReceptors as $index => $drugReceptor) {
                    $data[] = [
                        'DT_RowIndex' => $start + $index + 1,
                        'id' => $drugReceptor->id,
                        'receptor_name' => $drugReceptor->receptor_name ?? 'N/A',
                        'receptor_description' => $drugReceptor->receptor_description ?? 'N/A',
                        'status' => Helper::getStatusBadge($drugReceptor->status),
                        'created_by' => $drugReceptor->createdBy->full_name ?? 'N/A',
                        'created_at' => Helper::formatDate($drugReceptor->updated_at),
                        'actions' => Helper::getActionButtons($drugReceptor->id, 'drug-receptors', ['edit', 'delete'])
                    ];
                }

                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching drug receptors data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.drug-receptors.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.drug-receptors.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'receptor_name' => 'required|string|max:255',
                'receptor_description' => 'required',
                'status' => 'required|in:1,0',
            ]);
            $validatedData['created_by'] = Auth::id();
            DrugReceptors::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Drug Receptor created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating drug receptor: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the drug receptor.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $drugReceptor = DrugReceptors::findOrFail($id);
        return view('Web.drug-receptors.edit', compact('drugReceptor'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $drugReceptor = DrugReceptors::findOrFail($id);

            $validatedData = $request->validate([
                'receptor_name' => 'required|string|max:255',
                'receptor_description' => 'required',
                'status' => 'required|in:1,0',
            ]);

            $drugReceptor->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Drug Receptor updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating drug receptor: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the drug receptor.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $drugReceptor = DrugReceptors::findOrFail($id);
            $drugReceptor->delete();
            return response()->json(['success' => true, 'message' => 'Drug Receptor deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting drug receptor: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the drug receptor.'], 500);
        }
    }
}
