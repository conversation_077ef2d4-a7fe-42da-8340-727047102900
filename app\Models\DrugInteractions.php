<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DrugInteractions extends Model
{
    use SoftDeletes;

    protected $table = 'drug_Interactions';
    protected $fillable = [
        'drug_interaction',
        'drug_interaction_description',
        'created_by',
        'status',
    ];

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************
}
