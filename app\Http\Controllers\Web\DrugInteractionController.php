<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\DrugInteractions;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DrugInteractionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                // Define columns for ordering
                $columns = ['id', 'drug_interaction', 'drug_interaction_description', 'created_by', 'created_at'];
                $orderBy = $columns[$orderColumn] ?? 'id';

                // Base query
                $query = DrugInteractions::with('createdBy');

                // Apply search filter
                if (!empty($searchValue)) {
                    $query->where(function ($q) use ($searchValue) {
                        $q->where('drug_interaction', 'LIKE', "%{$searchValue}%")
                          ->orWhere('drug_interaction_description', 'LIKE', "%{$searchValue}%");
                    });
                }

                // Get total count before pagination
                $totalRecords = DrugInteractions::count();
                $filteredRecords = $query->count();

                // Apply ordering and pagination
                $drugInteractions = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

                // Format data for DataTable
                $data = [];
                foreach ($drugInteractions as $index => $drugInteraction) {
                    $data[] = [
                        'DT_RowIndex' => $start + $index + 1,
                        'id' => $drugInteraction->id,
                        'drug_interaction' => $drugInteraction->drug_interaction ?? 'N/A',
                        'drug_interaction_description' => $drugInteraction->drug_interaction_description ?? 'N/A',
                        'created_by' => $drugInteraction->createdBy->full_name ?? 'N/A',
                        'created_at' => Helper::formatDate($drugInteraction->updated_at),
                        'actions' => Helper::getActionButtons($drugInteraction->id, 'drug-Interaction', ['edit', 'delete'])
                    ];
                }

                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching drug Interaction data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.drug-Interaction.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.drug-Interaction.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'drug_interaction' => 'required|string|max:255',
                'drug_interaction_description' => 'required',
                'status' => 'required|in:1,0',
            ]);
            $validatedData['created_by'] = Auth::id();
            DrugInteractions::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Drug Interaction created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating drug Interaction: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the drug Interaction.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $drugInteraction = DrugInteractions::findOrFail($id);
        return view('Web.drug-Interaction.edit', compact('drugInteraction'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $drugInteraction = DrugInteractions::findOrFail($id);

            $validatedData = $request->validate([
                'drug_interaction' => 'required|string|max:255',
                'drug_interaction_description' => 'required',
                'status' => 'required|in:1,0',
            ]);

            $drugInteraction->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Drug Interaction updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating drug Interaction: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the drug Interaction.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $drugInteraction = DrugInteractions::findOrFail($id);
            $drugInteraction->delete();
            return response()->json(['success' => true, 'message' => 'Drug Interaction deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting drug Interaction: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the drug Interaction.'], 500);
        }
    }
}
