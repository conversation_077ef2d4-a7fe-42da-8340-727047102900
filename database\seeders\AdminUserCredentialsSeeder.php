<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminUserCredentialsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('users')->insert([
            [
                'first_name'        => '<PERSON>',
                'last_name'         => 'Meghwa',
                'email'             => '<EMAIL>',
                'email_verified_at' => Carbon::now(),
                'password'          => Hash::make('Pass@12345'),
                'created_at'        => Carbon::now(),
                'updated_at'        => Carbon::now(),
            ],
            [
                'first_name'        => 'Abhishek',
                'last_name'         => 'William',
                'email'             => '<EMAIL>',
                'email_verified_at' => Carbon::now(),
                'password'          => Hash::make('Pass@12345'),
                'created_at'        => Carbon::now(),
                'updated_at'        => Carbon::now(),
            ]
        ]);
    }
}
