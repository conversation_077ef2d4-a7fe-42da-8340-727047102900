@extends('Layouts.app')

@section('title', 'SamRx | Adverse Effect Details')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Adverse Effect Details<span class="page-desc text-muted fs-7 fw-semibold pt-1">View adverse effect information</span></h1>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="{{ route('adverse-effect.edit', $adverseEffect->id) }}" class="btn btn-warning"><i class="fas fa-edit"></i> Edit</a>
                                <a href="{{ route('adverse-effect.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to List</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <!-- Basic Information Card -->
                        <div class="card card-flush mb-6">
                            <div class="card-header">
                                <div class="card-title">
                                    <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                </div>
                            </div>
                            <div class="card-body pt-6">
                                <div class="row g-6">
                                    <!-- Drug Name -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label class="form-label fw-semibold fs-6 text-gray-700">Drug Name</label>
                                            <div class="fw-bold fs-6">{{ $adverseEffect->drug->generic_name ?? 'N/A' }}</div>
                                        </div>
                                    </div>

                                    <!-- Status -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label class="form-label fw-semibold fs-6 text-gray-700">Status</label>
                                            <div class="fw-bold fs-6">
                                                @if($adverseEffect->status == 1)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-danger">Inactive</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Adverse Effect Name -->
                                    <div class="col-md-12">
                                        <div class="fv-row">
                                            <label class="form-label fw-semibold fs-6 text-gray-700">Adverse Effect Name</label>
                                            <div class="fw-bold fs-6">{{ $adverseEffect->adverse_effect ?? 'N/A' }}</div>
                                        </div>
                                    </div>

                                    <!-- Description -->
                                    <div class="col-md-12">
                                        <div class="fv-row">
                                            <label class="form-label fw-semibold fs-6 text-gray-700">Description</label>
                                            <div class="fw-bold fs-6">{!! $adverseEffect->adverse_effect_description ?? 'N/A' !!}</div>
                                        </div>
                                    </div>

                                    <!-- Effect Type -->
                                    <div class="col-md-12">
                                        <div class="fv-row">
                                            <label class="form-label fw-semibold fs-6 text-gray-700">Effect Type</label>
                                            <div class="fw-bold fs-6">
                                                @if($adverseEffect->is_common)
                                                    <span class="badge badge-warning">Common</span>
                                                @elseif($adverseEffect->is_rare)
                                                    <span class="badge badge-info">Rare</span>
                                                @else
                                                    <span class="badge badge-secondary">Normal</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information Card -->
                        <div class="card card-flush">
                            <div class="card-header">
                                <div class="card-title">
                                    <h3 class="fw-bold text-dark"><i class="fas fa-clock text-primary me-2"></i>Additional Information</h3>
                                </div>
                            </div>
                            <div class="card-body pt-6">
                                <div class="row g-6">
                                    <!-- Created By -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label class="form-label fw-semibold fs-6 text-gray-700">Created By</label>
                                            <div class="fw-bold fs-6">{{ $adverseEffect->createdBy->full_name ?? 'N/A' }}</div>
                                        </div>
                                    </div>

                                    <!-- Created At -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label class="form-label fw-semibold fs-6 text-gray-700">Created At</label>
                                            <div class="fw-bold fs-6">{{ $adverseEffect->created_at ? $adverseEffect->created_at->format('d M Y, h:i A') : 'N/A' }}</div>
                                        </div>
                                    </div>

                                    <!-- Updated At -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label class="form-label fw-semibold fs-6 text-gray-700">Last Updated</label>
                                            <div class="fw-bold fs-6">{{ $adverseEffect->updated_at ? $adverseEffect->updated_at->format('d M Y, h:i A') : 'N/A' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
