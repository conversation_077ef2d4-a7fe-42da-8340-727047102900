@extends('Layouts.app')

@section('title', 'SamRx | Mechanism of Action')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div><h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Mechanism of Action Management<span class="page-desc text-muted fs-7 fw-semibold pt-1">Manage and organize mechanism of action information</span></h1></div>
                            <div><a href="{{ route('mechanism-of-action.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Mechanism of Action</a></div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="table-responsive">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="mechanism-of-action-table">
                                        <thead>
                                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                                <th class="min-w-50px">#</th>
                                                <th class="min-w-150px">Drug Name</th>
                                                <th class="min-w-150px">Mechanism of Action</th>
                                                <th class="min-w-150px">Created By</th>
                                                <th class="min-w-125px">Created At</th>
                                                <th class="text-end min-w-100px">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-gray-600 fw-semibold">
                                            <!-- Data will be loaded via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Define columns for mechanism of action table
            const columns = [
                getColumnDefinition('index'),
                { data: 'drug_id', name: 'drug_id' },
                { data: 'mechanism_of_action', name: 'mechanism_of_action' },
                { data: 'created_by', name: 'created_by' },
                getColumnDefinition('date', { data: 'created_at', name: 'created_at' }),
                getColumnDefinition('actions')
            ];

            // Initialize DataTable using helper function
            let table = initializeDataTable({
                tableId: 'mechanism-of-action-table',
                ajaxUrl: '{{ route("mechanism-of-action.index") }}',
                columns: columns,
                itemName: 'mechanism of action',
                order: [[4, 'desc']], // Order by created_at desc
                responsive: false, // Disable responsive feature to show all columns
                scrollX: true, // Enable horizontal scrolling
                language: {
                    emptyTable: '<div class="text-center py-4"><i class="fas fa-pills fa-3x text-muted mb-3"></i><br><span class="text-muted">No mechanism of actions found</span></div>'
                }
            });

            // Delete functionality using universal function
            $(document).on('click', '.delete-mechanism-of-action', function() {
                const mechanismOfActionId = $(this).data('id');
                universalDelete({id: mechanismOfActionId, url: '{{ route("mechanism-of-action.destroy", ":id") }}', itemName: 'mechanism of action', table: table});
            });
        });
    </script>
@endsection
