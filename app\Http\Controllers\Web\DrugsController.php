<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Drugs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Helpers\Helper;

class DrugsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check if this is an AJAX request for DataTable data
        if ($request->ajax()) {
            try {
            $draw = $request->get('draw');
            $start = $request->get('start');
            $length = $request->get('length');
            $searchValue = $request->get('search')['value'] ?? '';
            $orderColumn = $request->get('order')[0]['column'] ?? 0;
            $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

            // Define columns for ordering
            $columns = ['id', 'serial_no', 'generic_name', 'brand_name', 'iupac_name', 'created_by', 'created_at'];
            $orderBy = $columns[$orderColumn] ?? 'id';

            // Base query
            $query = Drugs::with('createdBy');

            // Apply search filter
            if (!empty($searchValue)) {
                $query->where(function ($q) use ($searchValue) {
                    $q->where('serial_no', 'LIKE', "%{$searchValue}%")
                      ->orWhere('generic_name', 'LIKE', "%{$searchValue}%")
                      ->orWhere('brand_name', 'LIKE', "%{$searchValue}%")
                      ->orWhere('iupac_name', 'LIKE', "%{$searchValue}%")
                      ->orWhere('drug_description', 'LIKE', "%{$searchValue}%")
                      ->orWhere('drug_indication', 'LIKE', "%{$searchValue}%");
                });
            }

            // Get total count before pagination
            $totalRecords = Drugs::count();
            $filteredRecords = $query->count();

            // Apply ordering and pagination
            $drugs = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

            // Format data for DataTable
            $data = [];
            foreach ($drugs as $index => $drug) {
                $data[] = [
                    'DT_RowIndex' => $start + $index + 1,
                    'id' => $drug->id,
                    'serial_no' => $drug->serial_no ?? 'N/A',
                    'generic_name' => $drug->generic_name ?? 'N/A',
                    'brand_name' => $drug->brand_name ?? 'N/A',
                    'iupac_name' => $drug->iupac_name ?? 'N/A',
                    'status' => Helper::getStatusBadge($drug->status),
                    'created_by' => $drug->createdBy->full_name ?? 'N/A',
                    'created_at' => Helper::formatDate($drug->updated_at),
                    'actions' => Helper::getActionButtons($drug->id, 'drugs')
                ];
            }

            return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching drugs data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        // Return the view for regular page loads
        return view('Web.drugs.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.drugs.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'generic_name' => 'required|string',
                'brand_name' => 'required|string',
                'iupac_name' => 'required|string',
                'drug_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'drug_description' => 'required|string',
                'drug_indication' => 'required|string',
                'drug_dosage' => 'required|string',
                'drug_route_of_administration' => 'required|string',
                'drug_frequency' => 'required|string',
                'drug_precautions' => 'required|string',
                'status' => 'required|in:1,0',
            ]);

            if ($request->hasFile('drug_image')) {
                $validatedData['drug_image'] = $request->file('drug_image')->store('drug_images', 'public');
            }

            $randoms = "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            $serioulNo = substr(str_shuffle($randoms), 0, 10);
            $validatedData['serial_no'] = $serioulNo;
            $validatedData['created_by'] = Auth::id();

            Drugs::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Drug created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating drug: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the drug.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $drug = Drugs::findOrFail($id);
        return view('Web.drugs.show', compact('drug'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $drug = Drugs::findOrFail($id);
        return view('Web.drugs.edit', compact('drug'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $drug = Drugs::findOrFail($id);

            $validatedData = $request->validate([
                'generic_name' => 'required|string',
                'brand_name' => 'required|string',
                'iupac_name' => 'required|string',
                'drug_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'drug_description' => 'required|string',
                'drug_indication' => 'required|string',
                'drug_dosage' => 'required|string',
                'drug_route_of_administration' => 'required|string',
                'drug_frequency' => 'required|string',
                'drug_precautions' => 'required|string',
                'status' => 'required|in:1,0',
            ]);

            if ($request->hasFile('drug_image')) {
                if ($drug->drug_image) {
                    Storage::disk('public')->delete($drug->drug_image);
                }
                $validatedData['drug_image'] = $request->file('drug_image')->store('drug_images', 'public');
            }
            $validatedData['created_by'] = Auth::id();
            $drug->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Drug updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating drug: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the drug.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $drug = Drugs::findOrFail($id);
            $drug->delete();
            return response()->json(['success' => true, 'message' => 'Drug deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting drug: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the drug.'], 500);
        }
    }
}
