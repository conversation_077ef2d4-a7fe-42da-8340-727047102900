<!DOCTYPE html>
<html lang="en">
	<head>
		<title>RX-Info | A full-service healthcare provider</title>
		<meta charset="utf-8" />
		<link rel="shortcut icon" href="{{ asset('media/logos/SamRXHard.png') }}"/>
		<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
		<link href="{{ asset('plugins/global/plugins.bundle.css') }}" rel="stylesheet" type="text/css" />
		<link href="{{ asset('css/style.bundle.css') }}" rel="stylesheet" type="text/css" />
	</head>

	<body id="kt_body" class="app-blank app-blank">
		<div class="d-flex flex-column flex-root" id="kt_app_root">
			<div class="d-flex flex-column flex-lg-row flex-column-fluid">
				<div class="d-flex flex-column flex-lg-row-fluid w-lg-50 p-10 order-2 order-lg-1">
					<div class="d-flex flex-center flex-column flex-lg-row-fluid">
						<div class="text-center">
							<img alt="Logo" src="{{ asset('media/logos/SamRXHard.png') }}" class="h-100px mb-4" />
							<p class="text-gray-400 fw-semibold fs-5 mb-6">Here is the admin panel login for <span class="fw-bold">SamR<sub>x</sub></span></p>
						</div>
						<div class="w-lg-500px p-10">
							<form class="form w-100" id="kt_sign_in_form" method="POST" action="{{ route('login') }}">
								@csrf
								<div class="text-center mb-11">
									<h1 class="text-dark fw-bolder mb-3">Sign In</h1>
								</div>
								<div class="fv-row mb-8">
									<input type="text" placeholder="Email" name="email" autocomplete="off" class="form-control bg-transparent" />
								</div>
								<div class="fv-row mb-3">
									<input type="password" placeholder="Password" name="password" autocomplete="off" class="form-control bg-transparent" />
								</div>
								<div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="remember" id="remember_me" />
										<label class="form-check-label text-gray-500" for="remember_me">Remember Me</label>
									</div>
									<a href="#" class="link-primary">Forgot Password ?</a>
								</div>
								<div class="d-grid mb-10"><button type="submit" class="btn btn-primary">Sign In</button></div>
								<div class="text-gray-500 text-center fw-semibold fs-6">Health is the greatest gift, contentment the greatest wealth.</div>
							</form>
						</div>
					</div>
				</div>
				<div class="d-flex flex-lg-row-fluid w-lg-50 bgi-size-cover bgi-position-center order-1 order-lg-2">
					<div class="d-flex flex-column flex-center py-7 py-lg-15 px-5 px-md-15 w-100">
						<a href="#" class="mb-0 mb-lg-12">
							<img alt="Logo" src="{{ asset('media/logos/SamRXHard.png') }}" class="h-60px h-lg-75px" />
						</a>
						<img class="d-none d-lg-block mx-auto w-275px w-md-50 w-xl-500px mb-10 mb-lg-20" src="{{ asset('media/misc/user-login.jpg') }}" alt="" />
						<div class="d-none d-lg-block text-dark fs-base text-center">
							In this platform,
							<a href="#" class="opacity-75-hover text-warning fw-bold me-1">SamR<sub>x</sub></a> manages and organizes complete details of medicines,
							<br />including their usage, dosage, side effects, and other related information.
							<br />The goal is to provide accurate and reliable medical data to help users make
							<a href="#" class="opacity-75-hover text-warning fw-bold me-1">informed healthcare decisions</a> with ease.
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
</html>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
	$(document).ready(function() {
		$('#kt_sign_in_form').on('submit', function(e) {
			e.preventDefault();

			$.ajax({
				headers: {
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
				type: 'POST',
				url: $(this).attr('action'), // Now points to /login
				data: $(this).serialize(),
				success: function(response) {
					if (response.success) {
						Swal.fire({
							icon: 'success',
							title: 'Welcome!',
							text: response.message,
							timer: 2000,
							showConfirmButton: false
						}).then(() => {
							window.location.href = response.redirect;
						});
					} else {
						Swal.fire({
							icon: 'error',
							title: 'Oops...',
							text: response.message
						});
					}
				},
				error: function(xhr) {
					Swal.fire({
						icon: 'error',
						title: 'Login Failed',
						text: xhr.responseJSON?.message || 'Invalid credentials!'
					});
				}
			});
		});
	});
</script>
