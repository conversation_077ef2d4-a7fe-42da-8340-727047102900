<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DrugReceptors extends Model
{
    use SoftDeletes;

    protected $table = 'drug_binding_receptors';
    protected $fillable = [
        'receptor_name',
        'receptor_description',
        'created_by',
        'status',
    ];

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************
}
