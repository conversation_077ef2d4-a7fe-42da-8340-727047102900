<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Classifications;
use App\Models\Drugs;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ClassificationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                // Define columns for ordering
                $columns = ['id', 'drug_id', 'class_name', 'sub_class_name', 'created_by', 'created_at'];
                $orderBy = $columns[$orderColumn] ?? 'id';

                // Base query
                $query = Classifications::with(['drug', 'createdBy']);

                // Apply search filter
                if (!empty($searchValue)) {
                    $query->where(function ($q) use ($searchValue) {
                        $q->whereHas('drug', function ($q) use ($searchValue) {
                            $q->where('generic_name', 'LIKE', "%{$searchValue}%")
                              ->orWhere('brand_name', 'LIKE', "%{$searchValue}%");
                        });
                        $q->orWhere('class_name', 'LIKE', "%{$searchValue}%")
                          ->orWhere('sub_class_name', 'LIKE', "%{$searchValue}%");
                    });
                }

                // Get total count before pagination
                $totalRecords = Classifications::count();
                $filteredRecords = $query->count();

                // Apply ordering and pagination
                $classifications = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

                // Format data for DataTable
                $data = [];
                foreach ($classifications as $index => $classification) {
                    $data[] = [
                        'DT_RowIndex' => $start + $index + 1,
                        'id' => $classification->id,
                        'drug_id' => $classification->drug->generic_name ?? 'N/A',
                        'class_name' => $classification->class_name ?? 'N/A',
                        'sub_class_name' => $classification->sub_class_name ?? 'N/A',
                        'created_by' => $classification->createdBy->full_name ?? 'N/A',
                        'created_at' => Helper::formatDate($classification->updated_at),
                        'actions' => Helper::getActionButtons($classification->id, 'classification', ['edit', 'delete'])
                    ];
                }

                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching classification data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.classification.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $drugs = Drugs::all();
        return view('Web.classification.create', compact('drugs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'class_name' => 'required|string|max:255',
                'drug_id' => 'required|exists:drugs,id',
                'sub_class_name' => 'required|string|max:255',
                'status' => 'required|in:1,0',
            ]);

            $validatedData['created_by'] = Auth::id();
            Classifications::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Classification created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating classification: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the classification.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $classification = Classifications::findOrFail($id);
        $drugs = Drugs::all();
        return view('Web.classification.edit', compact('classification', 'drugs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $classification = Classifications::findOrFail($id);

            $validatedData = $request->validate([
                'class_name' => 'required|string|max:255',
                'drug_id' => 'required|exists:drugs,id',
                'sub_class_name' => 'required|string|max:255',
                'status' => 'required|in:1,0',
            ]);

            $classification->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Classification updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating classification: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the classification.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $classification = Classifications::findOrFail($id);
            $classification->delete();
            return response()->json(['success' => true, 'message' => 'Classification deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting classification: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the classification.'], 500);
        }
    }
}
