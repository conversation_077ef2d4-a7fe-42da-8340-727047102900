@extends('Layouts.app')

@section('title', 'SamRx | Disease Specialists')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div><h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Disease Specialists Management<span class="page-desc text-muted fs-7 fw-semibold pt-1">Manage and organize disease specialists information</span></h1></div>
                            <div><a href="{{ route('disease-specialists.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Disease Specialist</a></div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="table-responsive">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="disease-specialists-table">
                                        <thead>
                                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                                <th class="min-w-50px">#</th>
                                                <th class="min-w-150px">Specialist Name</th>
                                                <th class="min-w-150px">Specialist Description</th>
                                                <th class="min-w-100px">Status</th>
                                                <th class="min-w-150px">Created By</th>
                                                <th class="min-w-125px">Created At</th>
                                                <th class="text-end min-w-100px">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-gray-600 fw-semibold">
                                            <!-- Data will be loaded via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Define columns for drug specialists table
            const columns = [
                getColumnDefinition('index'),
                { data: 'specialist_name', name: 'specialist_name' },
                { data: 'specialist_description', name: 'specialist_description' },
                getColumnDefinition('status', { data: 'status', name: 'status' }),
                { data: 'created_by', name: 'created_by' },
                getColumnDefinition('date', { data: 'created_at', name: 'created_at' }),
                getColumnDefinition('actions')
            ];

            // Initialize DataTable using helper function
            let table = initializeDataTable({
                tableId: 'disease-specialists-table',
                ajaxUrl: '{{ route("disease-specialists.index") }}',
                columns: columns,
                itemName: 'disease specialist',
                order: [[5, 'desc']], // Order by created_at desc
                responsive: false, // Disable responsive feature to show all columns
                scrollX: true, // Enable horizontal scrolling
                language: {
                    emptyTable: '<div class="text-center py-4"><i class="fas fa-pills fa-3x text-muted mb-3"></i><br><span class="text-muted">No drug specialists found</span></div>'
                }
            });

            // Delete functionality using universal function
            $(document).on('click', '.delete-disease-specialists', function() {
                const diseaseSpecialistId = $(this).data('id');
                universalDelete({id: diseaseSpecialistId, url: '{{ route("disease-specialists.destroy", ":id") }}', itemName: 'disease specialist', table: table});
            });
        });
    </script>
@endsection
