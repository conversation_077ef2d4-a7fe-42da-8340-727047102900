<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Classifications extends Model
{
    use SoftDeletes;

    protected $table = 'drug_classifications';
    protected $fillable = [
        'drug_id',
        'class_name',
        'sub_class_name',
        'created_by',
        'status',
    ];

    // ************Relation-With-Drugs************
    public function drug()
    {
        return $this->belongsTo(Drugs::class, 'drug_id');
    }
    // ************Relation-With-Drugs************

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************
}
