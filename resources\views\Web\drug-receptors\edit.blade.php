@extends('Layouts.app')

@section('title', 'SamRx | Edit Drug Receptor')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit Drug Receptor<span class="page-desc text-muted fs-7 fw-semibold pt-1">Update drug receptor information</span></h1>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="drug-receptor-edit-form" action="{{ route('drug-receptors.update', $drugReceptor->id) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Receptor Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="receptor_name" class="form-label fw-semibold fs-6 required">Receptor Name</label>
                                                <input type="text" name="receptor_name" id="receptor_name" class="form-control form-control-solid @error('receptor_name') is-invalid @enderror" placeholder="Enter receptor name" value="{{ $drugReceptor->receptor_name }}" required>
                                                @error('receptor_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the name of the drug receptor</div>
                                            </div>
                                        </div>

                                        <!-- Receptor Description -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="receptor_description" class="form-label fw-semibold fs-6 required">Receptor Description</label>
                                                <textarea name="receptor_description" id="receptor_description" class="form-control form-control-solid @error('receptor_description') is-invalid @enderror" rows="4" placeholder="Enter receptor description..." required>{{ $drugReceptor->receptor_description }}</textarea>
                                                @error('receptor_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the description of the drug receptor</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ $drugReceptor->status == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ $drugReceptor->status == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Set the status for this drug receptor</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Update Drug Receptor</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('drug-receptors.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'drug-receptor-edit-form',
                submitBtnId: 'submit-btn',
                exitSelector: 'a[href="{{ route("drug-receptors.index") }}"]',
                successMessage: 'Drug receptor has been updated successfully.',
                redirectUrl: '{{ route("drug-receptors.index") }}',
                hasFileUpload: false
            });
        });
    </script>
@endsection
