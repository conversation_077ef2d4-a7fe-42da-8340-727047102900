@extends('Layouts.master')

@section('title', 'CKEditor Test Page')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                        CKEditor Test Page
                    </h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted">
                            <a href="{{ route('dashboard') }}" class="text-muted text-hover-primary">Home</a>
                        </li>
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        </li>
                        <li class="breadcrumb-item text-muted">CKEditor Test</li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-xxl">
                
                <!-- Test Form 1: Basic CKEditor Test -->
                <div class="card card-flush mb-6">
                    <div class="card-header">
                        <div class="card-title">
                            <h2>Test 1: Basic CKEditor Initialization</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="test-form-1">
                            <div class="row g-6">
                                <div class="col-md-6">
                                    <label for="test_textarea_1" class="form-label fw-semibold fs-6">Auto-initialized Textarea</label>
                                    <textarea name="test_textarea_1" id="test_textarea_1" class="form-control form-control-solid" rows="4" placeholder="This should automatically get CKEditor...">
                                        <p>This is a <strong>test</strong> with <em>formatted</em> content.</p>
                                        <ul>
                                            <li>List item 1</li>
                                            <li>List item 2</li>
                                        </ul>
                                    </textarea>
                                </div>
                                <div class="col-md-6">
                                    <label for="test_textarea_2" class="form-label fw-semibold fs-6">Manual CKEditor Class</label>
                                    <textarea name="test_textarea_2" id="test_textarea_2" class="form-control form-control-solid ckeditor" rows="4" placeholder="This has explicit ckeditor class...">
                                        <h2>Heading Example</h2>
                                        <p>This textarea has the <code>ckeditor</code> class explicitly added.</p>
                                        <blockquote>This is a blockquote example.</blockquote>
                                    </textarea>
                                </div>
                            </div>
                            <div class="row g-6 mt-3">
                                <div class="col-md-6">
                                    <label for="test_textarea_3" class="form-label fw-semibold fs-6">Disabled CKEditor</label>
                                    <textarea name="test_textarea_3" id="test_textarea_3" class="form-control form-control-solid no-ckeditor" rows="4" placeholder="This should remain as plain textarea...">This textarea has the 'no-ckeditor' class and should remain as a plain textarea.</textarea>
                                </div>
                                <div class="col-md-6">
                                    <label for="test_textarea_4" class="form-label fw-semibold fs-6">Data Attribute Disabled</label>
                                    <textarea name="test_textarea_4" id="test_textarea_4" class="form-control form-control-solid" data-no-ckeditor="true" rows="4" placeholder="This should also remain plain...">This textarea has data-no-ckeditor="true" and should remain as a plain textarea.</textarea>
                                </div>
                            </div>
                            <div class="text-center mt-6">
                                <button type="submit" class="btn btn-primary" id="test-submit-1">
                                    <i class="fas fa-save me-2"></i>Test Form Submission
                                </button>
                                <button type="reset" class="btn btn-secondary ms-3">
                                    <i class="fas fa-undo me-2"></i>Reset Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Test Form 2: Enhanced Form Integration -->
                <div class="card card-flush mb-6">
                    <div class="card-header">
                        <div class="card-title">
                            <h2>Test 2: Enhanced Form Integration</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="test-form-2">
                            <div class="row g-6">
                                <div class="col-md-12">
                                    <label for="enhanced_textarea_1" class="form-label fw-semibold fs-6">Enhanced Form Textarea</label>
                                    <textarea name="enhanced_textarea_1" id="enhanced_textarea_1" class="form-control form-control-solid" rows="6" placeholder="This is managed by initializeEnhancedForm...">
                                        <h3>Enhanced Form Test</h3>
                                        <p>This textarea is part of an enhanced form that uses the <code>initializeEnhancedForm()</code> function.</p>
                                        <p>Features tested:</p>
                                        <ol>
                                            <li><strong>Automatic CKEditor initialization</strong></li>
                                            <li><em>Data synchronization on submit</em></li>
                                            <li>Form reset handling</li>
                                            <li>Validation integration</li>
                                        </ol>
                                    </textarea>
                                </div>
                            </div>
                            <div class="text-center mt-6">
                                <button type="submit" class="btn btn-success" id="test-submit-2">
                                    <i class="fas fa-check me-2"></i>Submit Enhanced Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="card card-flush">
                    <div class="card-header">
                        <div class="card-title">
                            <h2>Test Results & Debug Information</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-6">
                            <div class="col-md-6">
                                <h4>CKEditor Instances</h4>
                                <div id="ckeditor-instances" class="bg-light p-4 rounded">
                                    <em>Loading...</em>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h4>Form Data Preview</h4>
                                <div id="form-data-preview" class="bg-light p-4 rounded">
                                    <em>Submit a form to see data...</em>
                                </div>
                            </div>
                        </div>
                        <div class="row g-6 mt-3">
                            <div class="col-md-12">
                                <h4>Console Logs</h4>
                                <div id="console-logs" class="bg-dark text-light p-4 rounded" style="height: 200px; overflow-y: auto; font-family: monospace;">
                                    <div>CKEditor Test Page Loaded...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form for test form 2
            initializeEnhancedForm({
                formId: 'test-form-2',
                submitBtnId: 'test-submit-2',
                hasFileUpload: false,
                onSuccess: function(response) {
                    logToConsole('Enhanced form submitted successfully');
                    updateFormDataPreview('test-form-2');
                }
            });

            // Handle test form 1 submission manually
            $('#test-form-1').on('submit', function(e) {
                e.preventDefault();
                logToConsole('Test form 1 submitted');
                
                // Sync CKEditor data manually for this test
                syncAllCKEditorsInForm('#test-form-1');
                updateFormDataPreview('test-form-1');
                
                Swal.fire({
                    icon: 'success',
                    title: 'Test Successful',
                    text: 'Form data has been captured. Check the preview section.',
                    confirmButtonText: 'OK'
                });
            });

            // Update CKEditor instances display every 2 seconds
            setInterval(updateCKEditorInstances, 2000);
            
            // Initial update
            setTimeout(function() {
                updateCKEditorInstances();
                logToConsole('Initial CKEditor instances check completed');
            }, 1000);
        });

        function updateCKEditorInstances() {
            const instancesDiv = $('#ckeditor-instances');
            let html = '<strong>Active CKEditor Instances:</strong><br>';
            
            if (window.ckeditorInstances && Object.keys(window.ckeditorInstances).length > 0) {
                Object.keys(window.ckeditorInstances).forEach(function(id) {
                    const editor = window.ckeditorInstances[id];
                    const wordCount = editor.getData().replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length;
                    html += `<div class="mb-2">
                        <span class="badge badge-success">${id}</span> 
                        <small class="text-muted">(${wordCount} words)</small>
                    </div>`;
                });
            } else {
                html += '<em class="text-muted">No CKEditor instances found</em>';
            }
            
            instancesDiv.html(html);
        }

        function updateFormDataPreview(formId) {
            const form = $(`#${formId}`);
            const previewDiv = $('#form-data-preview');
            let html = `<strong>Form Data from ${formId}:</strong><br>`;
            
            form.find('textarea').each(function() {
                const name = $(this).attr('name');
                const value = $(this).val();
                const preview = value.length > 100 ? value.substring(0, 100) + '...' : value;
                html += `<div class="mb-2">
                    <strong>${name}:</strong><br>
                    <small class="text-muted">${preview}</small>
                </div>`;
            });
            
            previewDiv.html(html);
        }

        function logToConsole(message) {
            const logsDiv = $('#console-logs');
            const timestamp = new Date().toLocaleTimeString();
            logsDiv.append(`<div>[${timestamp}] ${message}</div>`);
            logsDiv.scrollTop(logsDiv[0].scrollHeight);
        }

        // Override console.log to capture CKEditor messages
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && args[0].toString().includes('CKEditor')) {
                logToConsole(args.join(' '));
            }
        };
    </script>
@endsection
