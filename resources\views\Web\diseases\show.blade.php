@extends('Layouts.app')

@section('title', 'SamRx | Disease Details')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Disease Details
                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">View comprehensive disease information</span>
                                </h1>
                            </div>
                            <div>
                                <a href="{{ route('diseases.edit', $disease->id) }}" class="btn btn-sm btn-warning me-2">
                                    <i class="fas fa-edit me-1"></i>Edit Disease
                                </a>
                                <a href="{{ route('diseases.index') }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Diseases
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Disease Information -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <!-- Basic Information Card -->
                        <div class="card card-flush mb-6">
                            <div class="card-header">
                                <div class="card-title">
                                    <h3 class="fw-bold text-dark">
                                        <i class="fas fa-virus text-primary me-2"></i>{{ $disease->disease_name }}
                                    </h3>
                                </div>
                                <div class="card-toolbar">
                                    {!! \App\Helpers\Helper::getStatusBadge($disease->status) !!}
                                </div>
                            </div>
                            <div class="card-body pt-6">
                                <div class="row g-6">
                                    <!-- Disease Cause -->
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-start">
                                            <div class="symbol symbol-50px me-4">
                                                <div class="symbol-label bg-light-danger">
                                                    <i class="fas fa-exclamation-triangle text-danger fs-2"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="fw-bold text-gray-800 mb-2">Disease Cause</h6>
                                                <p class="text-gray-600 fs-6 mb-0">{{ $disease->disease_cause ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Disease Symptoms -->
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-start">
                                            <div class="symbol symbol-50px me-4">
                                                <div class="symbol-label bg-light-warning">
                                                    <i class="fas fa-thermometer-half text-warning fs-2"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="fw-bold text-gray-800 mb-2">Symptoms</h6>
                                                <p class="text-gray-600 fs-6 mb-0">{{ $disease->disease_symptoms ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Disease Treatment -->
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-start">
                                            <div class="symbol symbol-50px me-4">
                                                <div class="symbol-label bg-light-success">
                                                    <i class="fas fa-pills text-success fs-2"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="fw-bold text-gray-800 mb-2">Treatment</h6>
                                                <p class="text-gray-600 fs-6 mb-0">{{ $disease->disease_treatment ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Disease Prevention -->
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-start">
                                            <div class="symbol symbol-50px me-4">
                                                <div class="symbol-label bg-light-info">
                                                    <i class="fas fa-shield-alt text-info fs-2"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="fw-bold text-gray-800 mb-2">Prevention</h6>
                                                <p class="text-gray-600 fs-6 mb-0">{{ $disease->disease_prevention ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    @if($disease->disease_first_found)
                                    <!-- Disease First Found -->
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-start">
                                            <div class="symbol symbol-50px me-4">
                                                <div class="symbol-label bg-light-primary">
                                                    <i class="fas fa-search text-primary fs-2"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="fw-bold text-gray-800 mb-2">First Found</h6>
                                                <p class="text-gray-600 fs-6 mb-0">{{ $disease->disease_first_found }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    @endif

                                    @if($disease->disease_effect)
                                    <!-- Disease Effect -->
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-start">
                                            <div class="symbol symbol-50px me-4">
                                                <div class="symbol-label bg-light-secondary">
                                                    <i class="fas fa-heartbeat text-secondary fs-2"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="fw-bold text-gray-800 mb-2">Effects</h6>
                                                <p class="text-gray-600 fs-6 mb-0">{{ $disease->disease_effect }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        @if($disease->disease_description)
                        <!-- Description Card -->
                        <div class="card card-flush mb-6">
                            <div class="card-header">
                                <div class="card-title">
                                    <h3 class="fw-bold text-dark">
                                        <i class="fas fa-file-alt text-primary me-2"></i>Description
                                    </h3>
                                </div>
                            </div>
                            <div class="card-body pt-6">
                                <div class="text-gray-600 fs-6">
                                    {!! $disease->disease_description !!}
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Metadata Card -->
                        <div class="card card-flush">
                            <div class="card-header">
                                <div class="card-title">
                                    <h3 class="fw-bold text-dark">
                                        <i class="fas fa-info-circle text-primary me-2"></i>Record Information
                                    </h3>
                                </div>
                            </div>
                            <div class="card-body pt-6">
                                <div class="row g-6">
                                    <div class="col-md-4">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user text-primary fs-4 me-3"></i>
                                            <div>
                                                <span class="fw-bold text-gray-800 d-block">Created By</span>
                                                <span class="text-gray-600 fs-7">{{ $disease->createdBy->full_name ?? 'N/A' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-plus text-success fs-4 me-3"></i>
                                            <div>
                                                <span class="fw-bold text-gray-800 d-block">Created At</span>
                                                <span class="text-gray-600 fs-7">{{ \App\Helpers\Helper::formatDate($disease->created_at) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-edit text-warning fs-4 me-3"></i>
                                            <div>
                                                <span class="fw-bold text-gray-800 d-block">Last Updated</span>
                                                <span class="text-gray-600 fs-7">{{ \App\Helpers\Helper::formatDate($disease->updated_at) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>
@endsection