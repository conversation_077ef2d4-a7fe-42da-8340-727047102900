<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\AdverseEffect;
use App\Models\Drugs;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Helpers\Helper;

class AdverseEffectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
            $draw = $request->get('draw');
            $start = $request->get('start');
            $length = $request->get('length');
            $searchValue = $request->get('search')['value'] ?? '';
            $orderColumn = $request->get('order')[0]['column'] ?? 0;
            $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

            // Define columns for ordering
            $columns = ['id', 'drug_id', 'adverse_effect', 'status', 'is_common', 'is_rare', 'created_by', 'created_at'];
            $orderBy = $columns[$orderColumn] ?? 'id';

            // Base query
            $query = AdverseEffect::with(['drug', 'createdBy']);

            // Apply search filter
            if (!empty($searchValue)) {
                $query->where(function ($q) use ($searchValue) {
                    $q->where('adverse_effect', 'LIKE', "%{$searchValue}%")
                      ->orWhere('adverse_effect_description', 'LIKE', "%{$searchValue}%")
                      ->orWhereHas('drug', function ($q) use ($searchValue) {
                          $q->where('generic_name', 'LIKE', "%{$searchValue}%")
                            ->orWhere('brand_name', 'LIKE', "%{$searchValue}%");
                      });
                });
            }

            // Get total count before pagination
            $totalRecords = AdverseEffect::count();
            $filteredRecords = $query->count();

            // Apply ordering and pagination
            $adverseEffects = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

            // Format data for DataTable
            $data = [];
            foreach ($adverseEffects as $index => $adverseEffect) {
                $data[] = [
                    'DT_RowIndex' => $start + $index + 1,
                    'id' => $adverseEffect->id,
                    'drug_id' => $adverseEffect->drug->generic_name ?? 'N/A',
                    'adverse_effect' => $adverseEffect->adverse_effect ?? 'N/A',
                    'status' => Helper::getStatusBadge($adverseEffect->status),
                    'type' => Helper::getAdverseEffectTypeBadge($adverseEffect->is_common, $adverseEffect->is_rare),
                    'created_by' => $adverseEffect->createdBy->full_name ?? 'N/A',
                    'created_at' => Helper::formatDate($adverseEffect->updated_at),
                    'actions' => Helper::getActionButtons($adverseEffect->id, 'adverse-effect')
                ];
            }

            return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching adverse effect data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        // Return the view for regular page loads
        return view('Web.adverse-effect.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $drugs = Drugs::all();
        return view('Web.adverse-effect.create', compact('drugs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'drug_id' => 'required|exists:drugs,id',
                'adverse_effect' => 'required',
                'adverse_effect_description' => 'required',
                'status' => 'required|in:1,0',
                'is_common' => 'nullable|boolean',
                'is_rare' => 'nullable|boolean',
            ]);

            $validatedData['created_by'] = Auth::id();
            $validatedData['is_common'] = $request->has('is_common') ? 1 : 0;
            $validatedData['is_rare'] = $request->has('is_rare') ? 1 : 0;

            AdverseEffect::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Adverse effect created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating adverse effect: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the adverse effect.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $adverseEffect = AdverseEffect::with(['drug', 'createdBy'])->findOrFail($id);
        return view('Web.adverse-effect.show', compact('adverseEffect'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $adverseEffect = AdverseEffect::findOrFail($id);
        $drugs = Drugs::all();
        return view('Web.adverse-effect.edit', compact('adverseEffect', 'drugs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $adverseEffect = AdverseEffect::findOrFail($id);

            $validatedData = $request->validate([
                'drug_id' => 'required|exists:drugs,id',
                'adverse_effect' => 'required',
                'adverse_effect_description' => 'required',
                'status' => 'required|in:1,0',
                'is_common' => 'nullable|boolean',
                'is_rare' => 'nullable|boolean',
            ]);

            $validatedData['created_by'] = Auth::id();
            $validatedData['is_common'] = $request->has('is_common') ? 1 : 0;
            $validatedData['is_rare'] = $request->has('is_rare') ? 1 : 0;

            $adverseEffect->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Adverse effect updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating adverse effect: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the adverse effect.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $adverseEffect = AdverseEffect::findOrFail($id);
            $adverseEffect->delete();
            return response()->json(['success' => true, 'message' => 'Adverse effect deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting adverse effect: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the adverse effect.'], 500);
        }
    }
}
