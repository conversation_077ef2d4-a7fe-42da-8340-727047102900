<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Drugs extends Model
{
    use SoftDeletes;

    protected $table = 'drugs';
    protected $fillable = [
        'serial_no',
        'generic_name',
        'brand_name',
        'iupac_name',
        'drug_image',
        'drug_description',
        'drug_indication',
        'drug_dosage',
        'drug_route_of_administration',
        'drug_frequency',
        'drug_precautions',
        'created_by',
        'status',
    ];

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************
}
