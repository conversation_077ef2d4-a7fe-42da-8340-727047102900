<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DrugSchedules extends Model
{
    use SoftDeletes;

    protected $table = 'schedule_or_otc';
    protected $fillable = [
        'schedule_name',
        'schedule_description',
        'created_by',
        'status',
    ];

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************
}
