<?php $__env->startSection('title', 'SamRx | Mechanism of Action Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex flex-column flex-column-fluid">
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-fluid">

            <!-- Page Header -->
            <div class="row mt-5 mb-5">
                <div class="col-12">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Mechanism of Action Details<span class="page-desc text-muted fs-7 fw-semibold pt-1">View mechanism of action information</span></h1>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(route('mechanism-of-action.edit', $mechanismOfAction->id)); ?>" class="btn btn-primary"><i class="fas fa-edit"></i> Edit</a>
                            <a href="<?php echo e(route('mechanism-of-action.index')); ?>" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to List</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="row g-5 g-xl-10">
                <div class="col-xl-12">
                    <div class="card card-flush">
                        <div class="card-body pt-5 px-5">
                            <div class="row g-6">
                                <!-- LEFT SIDE (Image + Info) -->
                                <div class="col-md-4">
                                    <div class="mb-4 text-center">
                                        <?php if($mechanismOfAction->moa_image): ?>
                                            <img src="<?php echo e(asset('storage/' . $mechanismOfAction->moa_image)); ?>"
                                                 alt="Mechanism of Action"
                                                 class="img-fluid rounded shadow-sm mb-3"
                                                 style="max-height: 300px; width: auto; object-fit: contain;">
                                        <?php else: ?>
                                            <div class="alert alert-secondary">No image available</div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Status + Last Updated + Created By -->
                                    <div class="d-flex flex-wrap gap-2 justify-content-center">
                                        <!-- Status -->
                                        <div>
                                            <?php if($mechanismOfAction->status === 1): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php elseif($mechanismOfAction->status === 0): ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Unknown</span>
                                            <?php endif; ?>
                                        </div>
                                        <!-- Last Updated -->
                                        <div class="text-muted small"><i class="fas fa-clock me-1"></i><?php echo e($mechanismOfAction->updated_at ? $mechanismOfAction->updated_at->format('d M Y, h:i A') : 'N/A'); ?></div>
                                        <!-- Created By -->
                                        <div class="text-muted small"><i class="fas fa-user me-1"></i><?php echo e($mechanismOfAction->createdBy->full_name ?? 'N/A'); ?></div>
                                    </div>
                                </div>

                                <!-- RIGHT SIDE (Details) -->
                                <div class="col-md-8">
                                    <!-- Drug Name -->
                                    <div class="mb-4">
                                        <label class="form-label fw-semibold fs-6">Drug Name</label>
                                        <div class="form-control form-control-solid bg-light"><?php echo e($mechanismOfAction->drug->generic_name ?? 'N/A'); ?></div>
                                    </div>

                                    <!-- Mechanism of Action -->
                                    <div class="mb-4">
                                        <label class="form-label fw-semibold fs-6">Mechanism of Action</label>
                                        <div class="form-control form-control-solid bg-light" style="min-height: 150px; overflow-y: auto;"><?php echo $mechanismOfAction->mechanism_of_action ?? 'N/A'; ?></div>
                                    </div>
                                </div>
                            </div> <!-- end row -->
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/Web/mechanism-of-action/show.blade.php ENDPATH**/ ?>