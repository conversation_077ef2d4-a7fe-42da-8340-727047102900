<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\DiseaseSpecialists;
use App\Models\Drugs;
use App\Models\DrugSchedules;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    // ***************************Login-Page-Show-Function*******************************
    public function showLoginForm()
    {
        return view('login');
    }
    // ***************************Login-Page-Show-Function*******************************

    // ***************************Login-Logic-Function*******************************
    public function login(Request $request)
    {
        try {
            $credentials = $request->only('email', 'password');
            if (auth()->attempt($credentials, $request->remember)) {
                return response()->json(['success' => true, 'message' => 'Login successful!', 'redirect' => route('admin-dashboard')]);
            }
            return response()->json(['success' => false, 'message' => 'Invalid email or password.'], 401);
        } catch (Exception $e) {
            Log::error('Login Error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while processing your request.'], 500);
        }
    }
    // ***************************Login-Logic-Function*******************************

    // ***************************Dashboard-Page-Show-Function*******************************
    public function showDashboard()
    {
        $totalDrugs = Drugs::count();
        $totalSpecialists = DiseaseSpecialists::count();
        $totalSchedules = DrugSchedules::count();
        return view('web.dashboard', compact('totalDrugs', 'totalSpecialists', 'totalSchedules'));
    }
    // ***************************Dashboard-Page-Show-Function*******************************

    // ***************************Logout-Function*******************************
    public function logout()
    {
        auth()->logout();
        return redirect()->route('showloginform');
    }
    // ***************************Logout-Function*******************************
}
