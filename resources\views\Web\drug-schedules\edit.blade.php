@extends('Layouts.app')

@section('title', 'SamRx | Edit Drug Schedule')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit Drug Schedule<span class="page-desc text-muted fs-7 fw-semibold pt-1">Update drug schedule information</span></h1>
                            </div>
                            <div><a href="{{ route('drug-schedules.index') }}" class="btn btn-primary"><i class="fas fa-list"></i> View All Drug Schedules</a></div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="drug-schedule-edit-form" action="{{ route('drug-schedules.update', $drugSchedule->id) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Schedule Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="schedule_name" class="form-label fw-semibold fs-6 required">Schedule Name</label>
                                                <input type="text" name="schedule_name" id="schedule_name" class="form-control form-control-solid @error('schedule_name') is-invalid @enderror" value="{{ $drugSchedule->schedule_name }}" placeholder="Enter schedule name" required>
                                                @error('schedule_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Name of the drug schedule</div>
                                            </div>
                                        </div>

                                        <!-- Schedule Description -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="schedule_description" class="form-label fw-semibold fs-6 required">Schedule Description</label>
                                                <textarea name="schedule_description" id="schedule_description" class="form-control form-control-solid @error('schedule_description') is-invalid @enderror" rows="4" placeholder="Enter schedule description..." required>{{ $drugSchedule->schedule_description }}</textarea>
                                                @error('schedule_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Description of the drug schedule</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ $drugSchedule->status == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ $drugSchedule->status == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Status of the drug schedule</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Update Drug Schedule</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('drug-schedules.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'drug-schedule-edit-form',
                submitBtnId: 'submit-btn',
                exitSelector: 'a[href="{{ route("drug-schedules.index") }}"]',
                successMessage: 'Drug schedule has been updated successfully.',
                redirectUrl: '{{ route("drug-schedules.index") }}',
                hasFileUpload: false
            });
        });
    </script>
@endsection
