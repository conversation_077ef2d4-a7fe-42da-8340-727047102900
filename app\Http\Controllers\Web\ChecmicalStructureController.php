<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\ChemicalStructure;
use App\Models\Drugs;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Helpers\Helper;

class ChecmicalStructureController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check if this is an AJAX request for DataTable data
        if ($request->ajax()) {
            try {
            $draw = $request->get('draw');
            $start = $request->get('start');
            $length = $request->get('length');
            $searchValue = $request->get('search')['value'] ?? '';
            $orderColumn = $request->get('order')[0]['column'] ?? 0;
            $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

            // Define columns for ordering
            $columns = ['id', 'drug_id', 'status', 'created_by', 'created_at'];
            $orderBy = $columns[$orderColumn] ?? 'id';

            // Base query
            $query = ChemicalStructure::with(['drug', 'createdBy']);

            // Apply search filter
            if (!empty($searchValue)) {
                $query->where(function ($q) use ($searchValue) {
                    $q->whereHas('drug', function ($q) use ($searchValue) {
                        $q->where('serial_no', 'LIKE', "%{$searchValue}%")
                          ->orWhere('generic_name', 'LIKE', "%{$searchValue}%")
                          ->orWhere('brand_name', 'LIKE', "%{$searchValue}%")
                          ->orWhere('iupac_name', 'LIKE', "%{$searchValue}%");
                    });
                });
            }

            // Get total count before pagination
            $totalRecords = ChemicalStructure::count();
            $filteredRecords = $query->count();

            // Apply ordering and pagination
            $chemicalStructures = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

            // Format data for DataTable
            $data = [];
            foreach ($chemicalStructures as $index => $chemicalStructure) {
                $data[] = [
                    'DT_RowIndex' => $start + $index + 1,
                    'id' => $chemicalStructure->id,
                    'drug_id' => $chemicalStructure->drug->generic_name ?? 'N/A',
                    'status' => Helper::getStatusBadge($chemicalStructure->status),
                    'created_by' => $chemicalStructure->createdBy->full_name ?? 'N/A',
                    'created_at' => Helper::formatDate($chemicalStructure->updated_at),
                    'actions' => Helper::getActionButtons($chemicalStructure->id, 'chemical-structure')
                ];
            }

            return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching chemical structure data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        // Return the view for regular page loads
        return view('Web.chemical-structure.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $drugs = Drugs::all();
        return view('Web.chemical-structure.create', compact('drugs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'drug_id' => 'required|exists:drugs,id',
                'chemical_structure_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
                'status' => 'required|in:1,0',
            ]);

            if ($request->hasFile('chemical_structure_image')) {
                $validatedData['chemical_structure'] = $request->file('chemical_structure_image')->store('chemical_structures', 'public');
            }
            $validatedData['created_by'] = Auth::id();
            ChemicalStructure::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Chemical structure created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating chemical structure: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the chemical structure.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $chemicalStructure = ChemicalStructure::findOrFail($id);
        return view('Web.chemical-structure.show', compact('chemicalStructure'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $chemicalStructure = ChemicalStructure::findOrFail($id);
        $drugs = Drugs::all();
        return view('Web.chemical-structure.edit', compact('chemicalStructure', 'drugs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $chemicalStructure = ChemicalStructure::findOrFail($id);

            $validatedData = $request->validate([
                'drug_id' => 'required|exists:drugs,id',
                'chemical_structure_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'status' => 'required|in:1,0',
            ]);

            if ($request->hasFile('chemical_structure_image')) {
                if ($chemicalStructure->chemical_structure) {
                    Storage::disk('public')->delete($chemicalStructure->chemical_structure);
                }
                $validatedData['chemical_structure'] = $request->file('chemical_structure_image')->store('chemical_structures', 'public');
            }
            $validatedData['created_by'] = Auth::id();
            $chemicalStructure->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Chemical structure updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating chemical structure: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the chemical structure.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $chemicalStructure = ChemicalStructure::findOrFail($id);
            $chemicalStructure->delete();
            return response()->json(['success' => true, 'message' => 'Chemical structure deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting chemical structure: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the chemical structure.'], 500);
        }
    }
}
