@extends('Layouts.app')

@section('title', 'SamRx | Chemical Structure Details')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Chemical Structure Details<span class="page-desc text-muted fs-7 fw-semibold pt-1">View comprehensive chemical structure information</span></h1>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="{{ route('chemical-structure.edit', $chemicalStructure->id) }}" class="btn btn-primary"><i class="fas fa-edit"></i> Edit Chemical Structure</a>
                                <a href="{{ route('chemical-structure.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Chemical Structure</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="row g-6 p-5">
                                    <!-- Drug Name -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="drug_id" class="form-label fw-semibold fs-6 required">Drug Name</label>
                                            <div class="form-control form-control-solid bg-light">{{ $chemicalStructure->drug->generic_name ?? 'N/A' }}</div>
                                        </div>
                                    </div>

                                    <!-- Chemical Structure Image -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="chemical_structure_image" class="form-label fw-semibold fs-6">Chemical Structure Image</label>
                                            <div class="form-control form-control-solid bg-light">
                                                @if($chemicalStructure->chemical_structure)
                                                    <img src="{{ asset('storage/' . $chemicalStructure->chemical_structure) }}" alt="Chemical Structure" class="img-fluid rounded shadow-sm" style="max-height: 300px; width: auto; object-fit: contain;">
                                                @else
                                                    No image available
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
