<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MechanismOfAction extends Model
{
    use SoftDeletes;

    protected $table = 'mechanism_of_actions';
    protected $fillable = [
        'drug_id',
        'mechanism_of_action',
        'moa_image',
        'created_by',
        'status',
    ];
    
    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************

    // ************Relation-With-Drugs************
    public function drug()
    {
        return $this->belongsTo(Drugs::class, 'drug_id');
    }
    // ************Relation-With-Drugs************
}
