<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\DrugSchedules;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DrugSchedulesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                // Define columns for ordering
                $columns = ['id', 'schedule_name', 'schedule_description', 'created_by', 'created_at'];
                $orderBy = $columns[$orderColumn] ?? 'id';

                // Base query
                $query = DrugSchedules::with('createdBy');

                // Apply search filter
                if (!empty($searchValue)) {
                    $query->where(function ($q) use ($searchValue) {
                        $q->where('schedule_name', 'LIKE', "%{$searchValue}%")
                          ->orWhere('schedule_description', 'LIKE', "%{$searchValue}%");
                    });
                }

                // Get total count before pagination
                $totalRecords = DrugSchedules::count();
                $filteredRecords = $query->count();

                // Apply ordering and pagination
                $drugSchedules = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

                // Format data for DataTable
                $data = [];
                foreach ($drugSchedules as $index => $drugSchedule) {
                    $data[] = [
                        'DT_RowIndex' => $start + $index + 1,
                        'id' => $drugSchedule->id,
                        'schedule_name' => $drugSchedule->schedule_name ?? 'N/A',
                        'schedule_description' => $drugSchedule->schedule_description ?? 'N/A',
                        'status' => Helper::getStatusBadge($drugSchedule->status),
                        'created_by' => $drugSchedule->createdBy->full_name ?? 'N/A',
                        'created_at' => Helper::formatDate($drugSchedule->updated_at),
                        'actions' => Helper::getActionButtons($drugSchedule->id, 'drug-schedules', ['edit', 'delete'])
                    ];
                }

                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching drug schedules data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.drug-schedules.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.drug-schedules.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'schedule_name' => 'required|string|max:255',
                'schedule_description' => 'required|string',
                'status' => 'required|in:1,0',
            ]);
            $validatedData['created_by'] = Auth::id();
            DrugSchedules::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Drug Schedule created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating drug schedule: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the drug schedule.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $drugSchedule = DrugSchedules::findOrFail($id);
        return view('Web.drug-schedules.edit', compact('drugSchedule'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $drugSchedule = DrugSchedules::findOrFail($id);

            $validatedData = $request->validate([
                'schedule_name' => 'required|string|max:255',
                'schedule_description' => 'required|string',
                'status' => 'required|in:1,0',
            ]);

            $drugSchedule->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Drug Schedule updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating drug schedule: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the drug schedule.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $drugSchedule = DrugSchedules::findOrFail($id);
            $drugSchedule->delete();
            return response()->json(['success' => true, 'message' => 'Drug Schedule deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting drug schedule: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the drug schedule.'], 500);
        }
    }
}
