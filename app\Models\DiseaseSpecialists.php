<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DiseaseSpecialists extends Model
{
    use SoftDeletes;

    protected $table = 'disease_specialists';
    protected $fillable = [
        'specialist_name',
        'specialist_description',
        'created_by',
        'status',
    ];

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************
}
