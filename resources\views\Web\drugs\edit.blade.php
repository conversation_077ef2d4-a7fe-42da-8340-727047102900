@extends('Layouts.app')

@section('title', 'SamRx | Edit Drug')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit Drug<span class="page-desc text-muted fs-7 fw-semibold pt-1">Update drug information</span></h1>
                            </div>
                            <div>
                                <a href="{{ route('drugs.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Drugs</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="drug-edit-form" action="{{ route('drugs.update', $drug->id) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Generic Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="generic_name" class="form-label fw-semibold fs-6 required">Generic Name</label>
                                                <input type="text" name="generic_name" id="generic_name" class="form-control form-control-solid @error('generic_name') is-invalid @enderror" placeholder="Enter generic name" value="{{ $drug->generic_name }}" required>
                                                @error('generic_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Scientific/generic name of the drug</div>
                                            </div>
                                        </div>

                                        <!-- Brand Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="brand_name" class="form-label fw-semibold fs-6 required">Brand Name</label>
                                                <input type="text" name="brand_name" id="brand_name" class="form-control form-control-solid @error('brand_name') is-invalid @enderror" placeholder="Enter brand name" value="{{ $drug->brand_name }}" required>
                                                @error('brand_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Commercial/brand name of the drug</div>
                                            </div>
                                        </div>

                                        <!-- IUPAC Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="iupac_name" class="form-label fw-semibold fs-6">IUPAC Name</label>
                                                <input type="text" name="iupac_name" id="iupac_name" class="form-control form-control-solid @error('iupac_name') is-invalid @enderror" placeholder="Enter IUPAC name" value="{{ $drug->iupac_name }}">
                                                @error('iupac_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">International Union of Pure and Applied Chemistry (IUPAC) name of the drug</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ old('status', $drug->status) == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ old('status', $drug->status) == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Current status of the drug</div>
                                            </div>
                                        </div>

                                        <!-- Drug Image -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_image" class="form-label fw-semibold fs-6">Drug Image</label>
                                                <input type="file" name="drug_image" id="drug_image" class="form-control form-control-solid @error('drug_image') is-invalid @enderror" accept="image/*">
                                                @error('drug_image')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Upload drug image (JPG, PNG, GIF - Max: 2MB)</div>
                                                <div id="image-preview" class="mt-3" style="display: none;">
                                                    <img id="preview-img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Detailed Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-file-medical text-success me-2"></i>Detailed Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Drug Description -->
                                        <div class="col-12">
                                            <div class="fv-row">
                                                <label for="drug_description" class="form-label fw-semibold fs-6 required">Drug Description</label>
                                                <textarea name="drug_description" id="drug_description" class="form-control form-control-solid @error('drug_description') is-invalid @enderror" rows="4" placeholder="Enter detailed description of the drug..." required>{{ $drug->drug_description }}</textarea>
                                                @error('drug_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Comprehensive description of the drug and its properties</div>
                                            </div>
                                        </div>

                                        <!-- Drug Indication -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_indication" class="form-label fw-semibold fs-6 required">Drug Indication</label>
                                                <textarea name="drug_indication" id="drug_indication" class="form-control form-control-solid @error('drug_indication') is-invalid @enderror" rows="4" placeholder="Enter medical conditions this drug treats..." required>{{ $drug->drug_indication }}</textarea>
                                                @error('drug_indication')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Medical conditions and symptoms this drug is used to treat</div>
                                            </div>
                                        </div>

                                        <!-- Drug Dosage -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_dosage" class="form-label fw-semibold fs-6 required">Drug Dosage</label>
                                                <textarea name="drug_dosage" id="drug_dosage" class="form-control form-control-solid @error('drug_dosage') is-invalid @enderror" rows="4" placeholder="Enter dosage information..." required>{{ $drug->drug_dosage }}</textarea>
                                                @error('drug_dosage')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Recommended dosage amounts and instructions</div>
                                            </div>
                                        </div>

                                        <!-- Route of Administration -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_route_of_administration" class="form-label fw-semibold fs-6 required">Route of Administration</label>
                                                <textarea name="drug_route_of_administration" id="drug_route_of_administration" class="form-control form-control-solid @error('drug_route_of_administration') is-invalid @enderror" rows="3" placeholder="Enter administration routes (oral, injection, etc.)..." required>{{ $drug->drug_route_of_administration }}</textarea>
                                                @error('drug_route_of_administration')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">How the drug should be administered (oral, IV, IM, etc.)</div>
                                            </div>
                                        </div>

                                        <!-- Drug Frequency -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_frequency" class="form-label fw-semibold fs-6 required">Drug Frequency</label>
                                                <textarea name="drug_frequency" id="drug_frequency" class="form-control form-control-solid @error('drug_frequency') is-invalid @enderror" rows="3" placeholder="Enter frequency (once daily, twice daily, etc.)..." required>{{ $drug->drug_frequency }}</textarea>
                                                @error('drug_frequency')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">How often the drug should be taken</div>
                                            </div>
                                        </div>

                                        <!-- Drug Precautions -->
                                        <div class="col-12">
                                            <div class="fv-row">
                                                <label for="drug_precautions" class="form-label fw-semibold fs-6 required">Drug Precautions</label>
                                                <textarea name="drug_precautions" id="drug_precautions" class="form-control form-control-solid @error('drug_precautions') is-invalid @enderror" rows="4" placeholder="Enter precautions, warnings contraindications..." required>{{ $drug->drug_precautions }}</textarea>
                                                @error('drug_precautions')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Important precautions, warnings, side effects, and contraindications</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Update Drug</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('drugs.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'drug-edit-form',
                submitBtnId: 'submit-btn',
                imageInputId: 'drug_image',
                imagePreviewId: 'image-preview',
                previewImageId: 'preview-img',
                exitSelector: 'a[href="{{ route("drugs.index") }}"]',
                successMessage: 'Drug has been updated successfully.',
                redirectUrl: '{{ route("drugs.index") }}',
                hasFileUpload: true
            });
        });
    </script>
@endsection
