<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChemicalStructure extends Model
{
    use SoftDeletes;

    protected $table = 'chemical_structures';
    protected $fillable = [
        'drug_id',
        'chemical_structure',
        'created_by',
        'status',
    ];

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************

    // ************Relation-With-Drugs************
    public function drug()
    {
        return $this->belongsTo(Drugs::class, 'drug_id');
    }
    // ************Relation-With-Drugs************
}
