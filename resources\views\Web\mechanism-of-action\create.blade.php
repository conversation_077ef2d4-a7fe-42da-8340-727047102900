@extends('Layouts.app')

@section('title', 'SamRx | Add New Mechanism of Action')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Add New Mechanism of Action<span class="page-desc text-muted fs-7 fw-semibold pt-1">Create a new mechanism of action entry</span></h1>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="mechanism-of-action-create-form" action="{{ route('mechanism-of-action.store') }}" method="POST">
                            @csrf
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Drug Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_id" class="form-label fw-semibold fs-6 required">Drug Name</label>
                                                <select name="drug_id" id="drug_id" class="form-select form-select-solid @error('drug_id') is-invalid @enderror" required>
                                                    <option value="">Select Drug</option>
                                                    @foreach($drugs as $drug)
                                                        <option value="{{ $drug->id }}" {{ old('drug_id') == $drug->id ? 'selected' : '' }}>
                                                            {{ $drug->generic_name }}{{ $drug->brand_name ? ' (' . $drug->brand_name . ')' : '' }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('drug_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Select the drug for this mechanism of action</div>
                                            </div>
                                        </div>

                                        <!-- Mechanism of Action -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="mechanism_of_action" class="form-label fw-semibold fs-6 required">Mechanism of Action</label>
                                                <textarea name="mechanism_of_action" id="mechanism_of_action" class="form-control form-control-solid ckeditor @error('mechanism_of_action') is-invalid @enderror" rows="4" placeholder="Enter mechanism of action..." required>{{ old('mechanism_of_action') }}</textarea>
                                                @error('mechanism_of_action')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the mechanism of action for the selected drug</div>
                                            </div>
                                        </div>

                                        <!-- Mechanism of Action Image -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="moa_image" class="form-label fw-semibold fs-6">Mechanism of Action Image</label>
                                                <input type="file" name="moa_image" id="moa_image" class="form-control form-control-solid @error('moa_image') is-invalid @enderror" accept="image/*">
                                                @error('moa_image')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Upload mechanism of action image (JPG, PNG, GIF - Max: 2MB)</div>
                                                <div id="image-preview" class="mt-3" style="display: none;">
                                                    <img id="preview-img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ old('status') == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ old('status') == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Set the status for this mechanism of action</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Save Mechanism of Action</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('mechanism-of-action.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'mechanism-of-action-create-form',
                submitBtnId: 'submit-btn',
                imageInputId: 'moa_image',
                imagePreviewId: 'image-preview',
                previewImageId: 'preview-img',
                exitSelector: 'a[href="{{ route("mechanism-of-action.index") }}"]',
                successMessage: 'Mechanism of action has been created successfully.',
                redirectUrl: '{{ route("mechanism-of-action.index") }}',
                hasFileUpload: true
            });
        });
    </script>
@endsection
