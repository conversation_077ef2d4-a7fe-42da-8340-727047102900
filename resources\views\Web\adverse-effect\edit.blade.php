@extends('Layouts.app')

@section('title', 'SamRx | Edit Adverse Effect')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit Adverse Effect<span class="page-desc text-muted fs-7 fw-semibold pt-1">Update adverse effect information</span></h1>
                            </div>
                            <div>
                                <a href="{{ route('adverse-effect.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Adverse Effects</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="adverse-effect-edit-form" action="{{ route('adverse-effect.update', $adverseEffect->id) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Drug Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_id" class="form-label fw-semibold fs-6 required">Drug Name</label>
                                                <select name="drug_id" id="drug_id" class="form-select form-select-solid @error('drug_id') is-invalid @enderror" required>
                                                    <option value="">Select Drug</option>
                                                    @foreach ($drugs as $drug)
                                                        <option value="{{ $drug->id }}" {{ $adverseEffect->drug_id == $drug->id ? 'selected' : '' }}>
                                                            {{ $drug->generic_name }}{{ $drug->brand_name ? ' (' . $drug->brand_name . ')' : '' }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('drug_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Search and select the drug for this adverse effect</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ $adverseEffect->status == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ $adverseEffect->status == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Set the status for this adverse effect</div>
                                            </div>
                                        </div>

                                        <!-- Adverse Effect Name -->
                                        <div class="col-md-12">
                                            <div class="fv-row">
                                                <label for="adverse_effect" class="form-label fw-semibold fs-6 required">Adverse Effect Name</label>
                                                <input type="text" name="adverse_effect" id="adverse_effect" class="form-control form-control-solid @error('adverse_effect') is-invalid @enderror" value="{{ $adverseEffect->adverse_effect }}" placeholder="Enter adverse effect name" required>
                                                @error('adverse_effect')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the name of the adverse effect</div>
                                            </div>
                                        </div>

                                        <!-- Adverse Effect Description -->
                                        <div class="col-md-12">
                                            <div class="fv-row">
                                                <label for="adverse_effect_description" class="form-label fw-semibold fs-6 required">Description</label>
                                                <textarea name="adverse_effect_description" id="adverse_effect_description" class="form-control form-control-solid ckeditor @error('adverse_effect_description') is-invalid @enderror" rows="4" placeholder="Enter detailed description of the adverse effect" required>{{ $adverseEffect->adverse_effect_description }}</textarea>
                                                @error('adverse_effect_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Provide a detailed description of the adverse effect</div>
                                            </div>
                                        </div>

                                        <!-- Effect Type -->
                                        <div class="col-md-12">
                                            <div class="fv-row">
                                                <label class="form-label fw-semibold fs-6">Effect Type</label>
                                                <div class="d-flex gap-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="is_common" id="is_common" value="1" {{ $adverseEffect->is_common ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="is_common">
                                                            Common Effect
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="is_rare" id="is_rare" value="1" {{ $adverseEffect->is_rare ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="is_rare">
                                                            Rare Effect
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="form-text">Select if this is a common or rare adverse effect</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Update Adverse Effect</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('adverse-effect.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'adverse-effect-edit-form',
                submitBtnId: 'submit-btn',
                exitSelector: 'a[href="{{ route("adverse-effect.index") }}"]',
                successMessage: 'Adverse effect has been updated successfully.',
                redirectUrl: '{{ route("adverse-effect.index") }}',
                hasFileUpload: false
            });
        });
    </script>
@endsection
