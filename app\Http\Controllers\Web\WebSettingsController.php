<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\WebSettings;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebSettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                // Define columns for ordering
                $columns = ['id', 'key', 'value', 'status', 'created_at'];
                $orderBy = $columns[$orderColumn] ?? 'id';

                // Base query
                $query = WebSettings::query();

                // Apply search filter
                if (!empty($searchValue)) {
                    $query->where(function ($q) use ($searchValue) {
                        $q->where('key', 'LIKE', "%{$searchValue}%")
                          ->orWhere('value', 'LIKE', "%{$searchValue}%");
                    });
                }

                // Get total count before pagination
                $totalRecords = WebSettings::count();
                $filteredRecords = $query->count();

                // Apply ordering and pagination
                $webSettings = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

                // Format data for DataTable
                $data = [];
                foreach ($webSettings as $index => $webSetting) {
                    $data[] = [
                        'DT_RowIndex' => $start + $index + 1,
                        'id' => $webSetting->id,
                        'key' => $webSetting->key ?? 'N/A',
                        'value' => $webSetting->value ?? 'N/A',
                        'status' => Helper::getStatusBadge($webSetting->status),
                        'created_at' => Helper::formatDate($webSetting->updated_at),
                        'actions' => Helper::getActionButtons($webSetting->id, 'web-settings', ['edit', 'delete'])
                    ];
                }

                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching web settings data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.web-settings.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.web-settings.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'key' => 'required|string|unique:web_settings',
                'value' => 'required|string',
                'status' => 'required|in:1,0',
            ]);
            WebSettings::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Web setting created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating web setting: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the web setting.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $webSetting = WebSettings::findOrFail($id);
        return view('Web.web-settings.edit', compact('webSetting'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $webSetting = WebSettings::findOrFail($id);

            $validatedData = $request->validate([
                'key' => 'required|string|unique:web_settings,key,' . $webSetting->id,
                'value' => 'required|string',
                'status' => 'required|in:1,0',
            ]);

            $webSetting->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Web setting updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating web setting: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the web setting.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $webSetting = WebSettings::findOrFail($id);
            $webSetting->delete();
            return response()->json(['success' => true, 'message' => 'Web setting deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting web setting: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the web setting.'], 500);
        }
    }
}
