/* CKEditor 5 Custom Styles */

/* Force border visibility for all CKEditor instances */
.ck.ck-editor,
.ck-editor__main,
div[class*="ck-editor"] {
    border: 1px solid #e4e6ef !important;
    border-radius: 0.475rem !important;
    box-shadow: none !important;
}

/* Main editor container */
.ck-editor {
    border: 1px solid #e4e6ef !important;
    border-radius: 0.475rem !important;
    overflow: hidden;
    box-shadow: none !important;
}

/* Ensure border is visible for form-control-solid class */
.form-control-solid + .ck-editor {
    border: 1px solid #e4e6ef !important;
    background-color: #f8f9fa;
}

/* Normal form-control styling */
.form-control + .ck-editor {
    border: 1px solid #ced4da !important;
    background-color: #ffffff;
}

/* Editor content area */
.ck-editor__editable {
    min-height: 200px !important;
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
    font-size: 14px;
    line-height: 1.6;
    border: none !important;
    box-shadow: none !important;
}

/* Focus state */
.ck-editor__editable:focus {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

/* Focus state for the main editor container */
.ck-editor.ck-focused {
    border-color: #80bdff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Hover state */
.ck-editor:hover {
    border-color: #b3d7ff !important;
}

/* Toolbar styling */
.ck-toolbar {
    border-bottom: 1px solid #e4e6ef !important;
    background: #f8f9fa !important;
    padding: 0.5rem !important;
}

/* Toolbar buttons */
.ck-toolbar .ck-button {
    border-radius: 0.25rem;
    margin: 0 0.125rem;
}

.ck-toolbar .ck-button:hover {
    background: #e9ecef !important;
}

.ck-toolbar .ck-button.ck-on {
    background: #007bff !important;
    color: white !important;
}

/* Dropdown styling */
.ck-dropdown__panel {
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

/* Content styling inside editor */
.ck-content {
    font-family: inherit;
}

.ck-content h1,
.ck-content h2,
.ck-content h3,
.ck-content h4,
.ck-content h5,
.ck-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.ck-content p {
    margin-bottom: 0.75rem;
}

.ck-content ul,
.ck-content ol {
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
}

.ck-content li {
    margin-bottom: 0.25rem;
}

.ck-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
}

.ck-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
}

.ck-content table td,
.ck-content table th {
    border: 1px solid #e4e6ef;
    padding: 0.5rem;
}

.ck-content table th {
    background: #f8f9fa;
    font-weight: 600;
}

/* Code block styling */
.ck-content pre {
    background: #f8f9fa;
    border: 1px solid #e4e6ef;
    border-radius: 0.25rem;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

/* Link styling */
.ck-content a {
    color: #007bff;
    text-decoration: underline;
}

.ck-content a:hover {
    color: #0056b3;
}

/* Image styling */
.ck-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ck-editor__editable {
        min-height: 150px !important;
        padding: 0.75rem;
        font-size: 13px;
    }

    .ck-toolbar {
        padding: 0.25rem !important;
    }

    .ck-toolbar .ck-button {
        margin: 0 0.0625rem;
    }
}

/* Error state styling */
.is-invalid + .ck-editor {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.is-invalid + .ck-editor .ck-editor__editable {
    border-color: #dc3545 !important;
}

/* Success state styling */
.is-valid + .ck-editor {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.is-valid + .ck-editor .ck-editor__editable {
    border-color: #28a745 !important;
}

/* Additional border enforcement */
.ck-editor,
.ck-editor.ck-editor__main {
    border: 1px solid #e4e6ef !important;
}

/* Form control solid editor styling */
.ck-editor.form-control-solid-editor {
    border: 1px solid #e4e6ef !important;
    background-color: #f8f9fa !important;
}

/* Invalid editor styling */
.ck-editor.is-invalid-editor {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* Valid editor styling */
.ck-editor.is-valid-editor {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

/* Ensure toolbar has proper border */
.ck-toolbar {
    border: none !important;
    border-bottom: 1px solid #e4e6ef !important;
    background: #f8f9fa !important;
    padding: 0.5rem !important;
}

/* Loading state */
.ck-editor.ck-editor--loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Placeholder styling */
.ck-editor__editable .ck-placeholder::before {
    color: #6c757d;
    font-style: italic;
}

/* Source editing mode */
.ck-source-editing-area {
    min-height: 200px !important;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

/* Balloon toolbar (for inline editing) */
.ck-balloon-panel {
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

/* Character counter (if enabled) */
.ck-word-count {
    font-size: 12px;
    color: #6c757d;
    text-align: right;
    padding: 0.5rem;
    border-top: 1px solid #e4e6ef;
    background: #f8f9fa;
}

/* Custom scrollbar for content area */
.ck-editor__editable::-webkit-scrollbar {
    width: 8px;
}

.ck-editor__editable::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.ck-editor__editable::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.ck-editor__editable::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Print styles */
@media print {
    .ck-toolbar {
        display: none !important;
    }

    .ck-editor__editable {
        border: none !important;
        box-shadow: none !important;
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
    }
}
