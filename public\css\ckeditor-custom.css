/* CKEditor 5 Custom Styles */

/* Force border visibility for all CKEditor instances */
.ck.ck-editor,
.ck-editor__main,
div[class*="ck-editor"] {
    border: 1px solid #e4e6ef !important;
    border-radius: 0.475rem !important;
    box-shadow: none !important;
}

/* Main editor container */
.ck-editor {
    border: 1px solid #e4e6ef !important;
    border-radius: 0.475rem !important;
    overflow: hidden;
    box-shadow: none !important;
}

/* Ensure border is visible for form-control-solid class */
.form-control-solid + .ck-editor {
    border: 1px solid #e4e6ef !important;
    background-color: #f8f9fa;
}

/* Normal form-control styling */
.form-control + .ck-editor {
    border: 1px solid #ced4da !important;
    background-color: #ffffff;
}

/* Editor content area */
.ck-editor__editable {
    min-height: 200px !important;
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
    font-size: 14px;
    line-height: 1.6;
    border: none !important;
    box-shadow: none !important;
}

/* Focus state */
.ck-editor__editable:focus {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

/* Focus state for the main editor container */
.ck-editor.ck-focused {
    border-color: #80bdff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Hover state */
.ck-editor:hover {
    border-color: #b3d7ff !important;
}

/* Toolbar styling */
.ck-toolbar {
    border-bottom: 1px solid #e4e6ef !important;
    background: #f8f9fa !important;
    padding: 0.5rem !important;
}

/* Toolbar buttons */
.ck-toolbar .ck-button {
    border-radius: 0.25rem;
    margin: 0 0.125rem;
}

.ck-toolbar .ck-button:hover {
    background: #e9ecef !important;
}

.ck-toolbar .ck-button.ck-on {
    background: #007bff !important;
    color: white !important;
}

/* Dropdown styling */
.ck-dropdown__panel {
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

/* Content styling inside editor */
.ck-content {
    font-family: inherit;
}

.ck-content h1,
.ck-content h2,
.ck-content h3,
.ck-content h4,
.ck-content h5,
.ck-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.ck-content p {
    margin-bottom: 0.75rem;
}

.ck-content ul,
.ck-content ol {
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
}

.ck-content li {
    margin-bottom: 0.25rem;
}

.ck-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
}

.ck-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
}

.ck-content table td,
.ck-content table th {
    border: 1px solid #e4e6ef;
    padding: 0.5rem;
}

.ck-content table th {
    background: #f8f9fa;
    font-weight: 600;
}

/* Code block styling */
.ck-content pre {
    background: #f8f9fa;
    border: 1px solid #e4e6ef;
    border-radius: 0.25rem;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

/* Link styling */
.ck-content a {
    color: #007bff;
    text-decoration: underline;
}

.ck-content a:hover {
    color: #0056b3;
}

/* Image styling */
.ck-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ck-editor__editable {
        min-height: 150px !important;
        padding: 0.75rem;
        font-size: 13px;
    }

    .ck-toolbar {
        padding: 0.25rem !important;
    }

    .ck-toolbar .ck-button {
        margin: 0 0.0625rem;
    }
}

/* Error state styling */
.is-invalid + .ck-editor {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.is-invalid + .ck-editor .ck-editor__editable {
    border-color: #dc3545 !important;
}

/* Success state styling */
.is-valid + .ck-editor {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.is-valid + .ck-editor .ck-editor__editable {
    border-color: #28a745 !important;
}

/* Additional border enforcement */
.ck-editor,
.ck-editor.ck-editor__main {
    border: 1px solid #e4e6ef !important;
}

/* Form control solid editor styling */
.ck-editor.form-control-solid-editor {
    border: 1px solid #e4e6ef !important;
    background-color: #f8f9fa !important;
}

/* Invalid editor styling */
.ck-editor.is-invalid-editor {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* Valid editor styling */
.ck-editor.is-valid-editor {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

/* Ensure toolbar has proper border */
.ck-toolbar {
    border: none !important;
    border-bottom: 1px solid #e4e6ef !important;
    background: #f8f9fa !important;
    padding: 0.5rem !important;
}

/* Loading state */
.ck-editor.ck-editor--loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Placeholder styling */
.ck-editor__editable .ck-placeholder::before {
    color: #6c757d;
    font-style: italic;
}

/* Source editing mode */
.ck-source-editing-area {
    min-height: 200px !important;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

/* Balloon toolbar (for inline editing) */
.ck-balloon-panel {
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

/* Character counter (if enabled) */
.ck-word-count {
    font-size: 12px;
    color: #6c757d;
    text-align: right;
    padding: 0.5rem;
    border-top: 1px solid #e4e6ef;
    background: #f8f9fa;
}

/* Custom scrollbar for content area */
.ck-editor__editable::-webkit-scrollbar {
    width: 8px;
}

.ck-editor__editable::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.ck-editor__editable::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.ck-editor__editable::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* CKEditor Content Display Styles (for show pages) */
.ckeditor-content {
    font-family: inherit;
    line-height: 1.6;
    color: inherit;
}

.ckeditor-content h1,
.ckeditor-content h2,
.ckeditor-content h3,
.ckeditor-content h4,
.ckeditor-content h5,
.ckeditor-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: inherit;
}

.ckeditor-content h1 { font-size: 1.5rem; }
.ckeditor-content h2 { font-size: 1.35rem; }
.ckeditor-content h3 { font-size: 1.2rem; }
.ckeditor-content h4 { font-size: 1.1rem; }
.ckeditor-content h5 { font-size: 1rem; }
.ckeditor-content h6 { font-size: 0.9rem; }

.ckeditor-content p {
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

.ckeditor-content ul,
.ckeditor-content ol {
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
}

.ckeditor-content li {
    margin-bottom: 0.25rem;
    line-height: 1.5;
}

.ckeditor-content ul li {
    list-style-type: disc;
}

.ckeditor-content ol li {
    list-style-type: decimal;
}

.ckeditor-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background: rgba(0, 123, 255, 0.05);
    padding: 1rem;
    border-radius: 0.25rem;
}

.ckeditor-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    font-size: 0.9rem;
}

.ckeditor-content table td,
.ckeditor-content table th {
    border: 1px solid #e4e6ef;
    padding: 0.5rem;
    text-align: left;
}

.ckeditor-content table th {
    background: #f8f9fa;
    font-weight: 600;
}

.ckeditor-content pre {
    background: #f8f9fa;
    border: 1px solid #e4e6ef;
    border-radius: 0.25rem;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
}

.ckeditor-content code {
    background: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.ckeditor-content a {
    color: #007bff;
    text-decoration: underline;
}

.ckeditor-content a:hover {
    color: #0056b3;
    text-decoration: none;
}

.ckeditor-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25rem;
    margin: 0.5rem 0;
}

.ckeditor-content strong,
.ckeditor-content b {
    font-weight: 600;
}

.ckeditor-content em,
.ckeditor-content i {
    font-style: italic;
}

.ckeditor-content u {
    text-decoration: underline;
}

.ckeditor-content s,
.ckeditor-content strike {
    text-decoration: line-through;
}

/* Responsive adjustments for content */
@media (max-width: 768px) {
    .ckeditor-content {
        font-size: 0.9rem;
    }

    .ckeditor-content table {
        font-size: 0.8rem;
    }

    .ckeditor-content pre,
    .ckeditor-content code {
        font-size: 0.75rem;
    }
}

/* Empty content styling */
.ckeditor-content:empty::before {
    content: "No content available";
    color: #6c757d;
    font-style: italic;
}

/* Print styles */
@media print {
    .ck-toolbar {
        display: none !important;
    }

    .ck-editor__editable {
        border: none !important;
        box-shadow: none !important;
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
    }

    .ckeditor-content {
        font-size: 12pt;
        line-height: 1.4;
    }

    .ckeditor-content a {
        color: #000 !important;
        text-decoration: underline !important;
    }
}
