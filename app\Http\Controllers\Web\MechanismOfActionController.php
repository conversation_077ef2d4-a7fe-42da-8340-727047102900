<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Drugs;
use App\Models\MechanismOfAction;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Termwind\Components\Raw;

class MechanismOfActionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                // Define columns for ordering
                $columns = ['id', 'drug_id', 'mechanism_of_action', 'created_by', 'created_at'];
                $orderBy = $columns[$orderColumn] ?? 'id';

                // Base query
                $query = MechanismOfAction::with(['drug', 'createdBy']);

                // Apply search filter
                if (!empty($searchValue)) {
                    $query->where(function ($q) use ($searchValue) {
                        $q->whereHas('drug', function ($q) use ($searchValue) {
                            $q->where('generic_name', 'LIKE', "%{$searchValue}%")
                              ->orWhere('brand_name', 'LIKE', "%{$searchValue}%");
                        });
                        $q->orWhere('mechanism_of_action', 'LIKE', "%{$searchValue}%");
                    });
                }

                // Get total count before pagination
                $totalRecords = MechanismOfAction::count();
                $filteredRecords = $query->count();

                // Apply ordering and pagination
                $mechanismOfActions = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

                // Format data for DataTable
                $data = [];
                foreach ($mechanismOfActions as $index => $mechanismOfAction) {
                    $data[] = [
                        'DT_RowIndex' => $start + $index + 1,
                        'id' => $mechanismOfAction->id,
                        'drug_id' => $mechanismOfAction->drug->generic_name ?? 'N/A',
                        'mechanism_of_action' => $mechanismOfAction->mechanism_of_action ?? 'N/A',
                        'created_by' => $mechanismOfAction->createdBy->full_name ?? 'N/A',
                        'created_at' => Helper::formatDate($mechanismOfAction->updated_at),
                        'actions' => Helper::getActionButtons($mechanismOfAction->id, 'mechanism-of-action')
                    ];
                }

                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching mechanism of action data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.mechanism-of-action.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $drugs = Drugs::all();
        return view('Web.mechanism-of-action.create', compact('drugs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'drug_id' => 'required|exists:drugs,id',
                'mechanism_of_action' => 'required|string',
                'moa_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'status' => 'required|in:1,0',
            ]);

            if ($request->hasFile('moa_image')) {
                $validatedData['moa_image'] = $request->file('moa_image')->store('moa_images', 'public');
            }

            $validatedData['created_by'] = Auth::id();
            MechanismOfAction::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Mechanism of action created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating mechanism of action: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the mechanism of action.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $mechanismOfAction = MechanismOfAction::with(['drug', 'createdBy'])->findOrFail($id);
        return view('Web.mechanism-of-action.show', compact('mechanismOfAction'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $mechanismOfAction = MechanismOfAction::findOrFail($id);
        $drugs = Drugs::all();
        return view('Web.mechanism-of-action.edit', compact('mechanismOfAction', 'drugs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $mechanismOfAction = MechanismOfAction::findOrFail($id);

            $validatedData = $request->validate([
                'drug_id' => 'required|exists:drugs,id',
                'mechanism_of_action' => 'required|string',
                'moa_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'status' => 'required|in:1,0',
            ]);

            if ($request->hasFile('moa_image')) {
                if ($mechanismOfAction->moa_image) {
                    Storage::disk('public')->delete($mechanismOfAction->moa_image);
                }
                $validatedData['moa_image'] = $request->file('moa_image')->store('moa_images', 'public');
            }

            $mechanismOfAction->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Mechanism of action updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating mechanism of action: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the mechanism of action.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $mechanismOfAction = MechanismOfAction::findOrFail($id);
            $mechanismOfAction->delete();
            return response()->json(['success' => true, 'message' => 'Mechanism of action deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting mechanism of action: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the mechanism of action.'], 500);
        }
    }
}
