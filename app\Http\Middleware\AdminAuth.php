<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            // Try to authenticate using remember token if available
            if (!$this->attemptRememberLogin($request)) {
                // For AJAX requests, return JSON response
                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => 'Unauthorized. Please login to continue.', 'redirect' => route('showloginform')], 401);
                }
                // For regular requests, redirect to login
                return redirect()->route('showloginform')->with('error', 'Please login to access this area.');
            }
        }

        // Handle session timeout based on remember me status
        if (!$this->handleSessionTimeout($request)) {
            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'message' => 'Session expired. Please login again.', 'redirect' => route('showloginform')], 401);
            }
            return redirect()->route('showloginform')->with('error', 'Your session has expired. Please login again.');
        }

        // Update last activity timestamp
        session(['last_activity' => time()]);
        return $next($request);
    }

    /**
     * Attempt to login using remember token
     */
    private function attemptRememberLogin(Request $request): bool
    {
        // Check if remember token exists in cookies
        $rememberToken = $request->cookie(Auth::getRecallerName());

        if (!$rememberToken) {
            return false;
        }

        // Attempt to authenticate using remember token
        try {
            if (Auth::viaRemember()) {
                // Successfully authenticated via remember token
                session(['last_activity' => time(), 'remembered_login' => true]);
                return true;
            }
        } catch (\Exception) {
            // Remove invalid remember token
            $this->clearRememberToken();
        }

        return false;
    }

    /**
     * Handle session timeout based on remember me status
     */
    private function handleSessionTimeout(Request $request): bool
    {
        $lastActivity = session('last_activity');
        $isRemembered = session('remembered_login', false);

        // If user was remembered, extend session timeout significantly
        if ($isRemembered) {
            $timeout = config('auth.remember_timeout', 43200) * 60; // Default 30 days in seconds
        } else {
            $timeout = config('session.lifetime') * 60; // Convert minutes to seconds
        }

        if ($lastActivity && (time() - $lastActivity > $timeout)) {
            Auth::logout();
            session()->flush();
            $this->clearRememberToken();
            return false;
        }

        return true;
    }

    /**
     * Clear remember token cookie
     */
    private function clearRememberToken(): void
    {
        if (request()->hasCookie(Auth::getRecallerName())) {
            cookie()->queue(cookie()->forget(Auth::getRecallerName()));
        }
    }
}
