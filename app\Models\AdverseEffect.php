<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdverseEffect extends Model
{
    use SoftDeletes;

    protected $table = 'adverse_effects';
    protected $fillable = [
        'drug_id',
        'adverse_effect',
        'adverse_effect_description',
        'created_by',
        'status',
        'is_common',
        'is_rare',
    ];

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************

    // ************Relation-With-Drugs************
    public function drug()
    {
        return $this->belongsTo(Drugs::class, 'drug_id');
    }
    // ************Relation-With-Drugs************
}
