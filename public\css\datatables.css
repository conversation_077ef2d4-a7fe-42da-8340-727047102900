/* ================================ DATATABLES CUSTOM STYLES ================================ */

/* DataTables Wrapper Styling */
.dataTables_wrapper {
    font-family: 'Inter', sans-serif;
}

/* Override DataTables default styles to match your theme */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    color: inherit;
}

/* Ensure proper spacing */
.dataTables_wrapper .row {
    margin: 0;
}

.dataTables_wrapper .col-sm-12,
.dataTables_wrapper .col-md-6 {
    padding: 0;
}

.dataTables_wrapper .dataTables_length select {
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: #fff;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #495057;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    margin-left: 0.5rem;
    background-color: #fff;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #495057;
}

.dataTables_wrapper .dataTables_filter input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Table Styling */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Loading States */
.table-loading {
    position: relative;
    pointer-events: none;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
}

.badge-success {
    background-color: #28a745;
    color: #fff;
}

.badge-danger {
    background-color: #dc3545;
    color: #fff;
}

.badge-secondary {
    background-color: #6c757d;
    color: #fff;
}

/* Action Buttons */
.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* DataTables Pagination */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin-left: 0.125rem;
    line-height: 1.25;
    color: #007bff;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* DataTables Info */
.dataTables_wrapper .dataTables_info {
    padding-top: 0.75rem;
    color: #6c757d;
    font-size: 0.875rem;
}

/* Search and Length Controls */
.dataTables_wrapper .dataTables_length {
    margin-top: 0.4rem;
}

.dataTables_wrapper .dataTables_filter {
    margin-bottom: 1rem;
    text-align: right;
}

.dataTables_wrapper .dataTables_length label,
.dataTables_wrapper .dataTables_filter label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dataTables_wrapper .dataTables_filter {
        text-align: left;
        margin-top: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-sm {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
}

/* Custom Filter Controls */
.card-toolbar .form-select {
    min-width: 120px;
}

.card-toolbar .form-label {
    white-space: nowrap;
    margin-right: 0.5rem;
}

/* Empty State */
.dataTables_empty {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

/* Processing Indicator */
.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Dark Theme Support */
[data-theme="dark"] .dataTables_wrapper .dataTables_length select,
[data-theme="dark"] .dataTables_wrapper .dataTables_filter input {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-theme="dark"] .table thead th {
    background-color: #2d3748;
    color: #e2e8f0;
    border-bottom-color: #4a5568;
}

[data-theme="dark"] .table tbody td {
    background-color: #1a202c;
    color: #e2e8f0;
    border-top-color: #4a5568;
}

[data-theme="dark"] .table tbody tr:hover {
    background-color: #2d3748;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #4a5568;
    border-color: #4a5568;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_info {
    color: #a0aec0;
}

/* Integration with existing card styles */
.card .dataTables_wrapper {
    padding: 0;
}

.card-body .dataTables_wrapper .dataTables_length {
    margin-bottom: 1rem;
}

.card-body .dataTables_wrapper .dataTables_filter {
    margin-bottom: 1.5rem;
}

/* Fix for existing theme button styles */
.dataTables_wrapper .btn {
    display: inline-block;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Ensure proper table styling within cards */
.card .table {
    --bs-table-bg: transparent;
}

.card .table > :not(caption) > * > * {
    padding: 0.75rem;
    background-color: var(--bs-table-bg);
    border-bottom-width: 1px;
    box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}

/* Fix pagination alignment */
.dataTables_wrapper .dataTables_paginate {
    float: right;
    text-align: right;
    padding-top: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        text-align: center;
        margin-bottom: 1rem;
    }

    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        float: none;
        text-align: center;
        margin-top: 1rem;
    }
}
