<?php

namespace App\Helpers;

class Helper
{
    /**
     * Generate status badge HTML
     *
     * @param string|int $status
     * @return string
     */
    public static function getStatusBadge($status)
    {
        switch ($status) {
            case '1':
            case 1:
                return '<span class="badge badge-success">Active</span>';
            case '0':
            case 0:
                return '<span class="badge badge-danger">Inactive</span>';
            default:
                return '<span class="badge badge-secondary">Unknown</span>';
        }
    }

    /**
     * Generate action buttons HTML for any module
     *
     * @param int $id
     * @param string $routePrefix
     * @param array $actions - Array of actions to show ['show', 'edit', 'delete']
     * @return string
     */
    public static function getActionButtons($id, $routePrefix, $actions = ['show', 'edit', 'delete'])
    {
        $buttons = '<div class="d-flex align-items-center gap-2">';

        foreach ($actions as $action) {
            switch ($action) {
                case 'show':
                    $buttons .= '<a href="' . route($routePrefix . '.show', $id) . '" class="btn btn-sm btn-primary" title="View Details">
                        <i class="fas fa-eye"></i>
                    </a>';
                    break;

                case 'edit':
                    $buttons .= '<a href="' . route($routePrefix . '.edit', $id) . '" class="btn btn-sm btn-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                    break;

                case 'delete':
                    // Convert route prefix to delete class name
                    $deleteClass = 'delete-' . str_replace(['.', '_'], '-', $routePrefix);
                    $buttons .= '<button type="button" class="btn btn-sm btn-danger ' . $deleteClass . '" data-id="' . $id . '" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';
                    break;
            }
        }

        $buttons .= '</div>';
        return $buttons;
    }

    /**
     * Generate serial number for entities
     *
     * @param int $length
     * @return string
     */
    public static function generateSerialNumber($length = 10)
    {
        $randoms = "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        return substr(str_shuffle($randoms), 0, $length);
    }

    /**
     * Format date for display
     *
     * @param mixed $date
     * @param string $format
     * @return string
     */
    public static function formatDate($date, $format = 'd M Y, h:i A')
    {
        if (!$date) {
            return 'N/A';
        }

        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        }

        return $date->format($format);
    }

    /**
     * Get common/rare badge for adverse effects
     *
     * @param int $isCommon
     * @param int $isRare
     * @return string
     */
    public static function getAdverseEffectTypeBadge($isCommon, $isRare)
    {
        if ($isCommon) {
            return '<span class="badge badge-warning">Common</span>';
        } elseif ($isRare) {
            return '<span class="badge badge-info">Rare</span>';
        } else {
            return '<span class="badge badge-secondary">Normal</span>';
        }
    }
}
