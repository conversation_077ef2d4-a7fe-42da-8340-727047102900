<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('adverse_effects', function (Blueprint $table) {
            $table->longText('adverse_effect')->change();
            $table->longText('adverse_effect_description')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('adverse_effects', function (Blueprint $table) {
            $table->string('adverse_effect')->change();
            $table->string('adverse_effect_description')->change();
        });
    }
};
