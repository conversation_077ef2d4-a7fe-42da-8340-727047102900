<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('disease', function (Blueprint $table) {
            $table->longText('disease_cause')->change();
            $table->longText('disease_symptoms')->change();
            $table->longText('disease_treatment')->change();
            $table->longText('disease_prevention')->change();
            $table->longText('disease_first_found')->change();
            $table->longText('disease_effect')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('disease', function (Blueprint $table) {
            $table->string('disease_cause')->change();
            $table->string('disease_symptoms')->change();
            $table->string('disease_treatment')->change();
            $table->string('disease_prevention')->change();
            $table->string('disease_first_found')->change();
            $table->string('disease_effect')->change();
        });
    }
};
