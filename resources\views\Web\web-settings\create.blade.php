@extends('Layouts.app')

@section('title', 'SamRx | Add New Web Setting')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Add New Web Setting<span class="page-desc text-muted fs-7 fw-semibold pt-1">Create a new web setting entry</span></h1>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="web-setting-create-form" action="{{ route('web-settings.store') }}" method="POST">
                            @csrf
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Key -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="key" class="form-label fw-semibold fs-6 required">Key</label>
                                                <input type="text" name="key" id="key" class="form-control form-control-solid @error('key') is-invalid @enderror" placeholder="Enter key" value="{{ old('key') }}" required>
                                                @error('key')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the key for the web setting</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ old('status') == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ old('status') == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Set the status for this web setting</div>
                                            </div>
                                        </div>

                                        <!-- Value -->
                                        <div class="col-md-12">
                                            <div class="fv-row">
                                                <label for="value" class="form-label fw-semibold fs-6 required">Value</label>
                                                <textarea name="value" id="value" class="form-control form-control-solid ckeditor @error('value') is-invalid @enderror" placeholder="Enter value" required>{{ old('value') }}</textarea>
                                                @error('value')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the value for the web setting</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Save Web Setting</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('web-settings.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize CKEditor for value field
            // initializeCKEditor('#value', {
            //     placeholder: 'Enter the value for the web setting...'
            // });

            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'web-setting-create-form',
                submitBtnId: 'submit-btn',
                exitSelector: 'a[href="{{ route("web-settings.index") }}"]',
                successMessage: 'Web setting has been created successfully.',
                redirectUrl: '{{ route("web-settings.index") }}',
                hasFileUpload: false
            });
        });
    </script>
@endsection
