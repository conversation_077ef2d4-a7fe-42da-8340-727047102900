

<?php $__env->startSection('title', 'SamRx | Add New Disease'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Add New Disease<span class="page-desc text-muted fs-7 fw-semibold pt-1">Create a new disease entry with comprehensive information</span></h1>
                            </div>
                            <div>
                                <a href="<?php echo e(route('diseases.index')); ?>" class="btn btn-sm btn-primary">Back to Diseases</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="disease-create-form" action="<?php echo e(route('diseases.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Disease Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_name" class="form-label fw-semibold fs-6 required">Disease Name</label>
                                                <input type="text" name="disease_name" id="disease_name" class="form-control form-control-solid <?php $__errorArgs = ['disease_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter disease name" value="<?php echo e(old('disease_name')); ?>" required>
                                                <?php $__errorArgs = ['disease_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>

                                        <!-- Disease Cause -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_cause" class="form-label fw-semibold fs-6 required">Disease Cause</label>
                                                <input type="text" name="disease_cause" id="disease_cause" class="form-control form-control-solid <?php $__errorArgs = ['disease_cause'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter disease cause" value="<?php echo e(old('disease_cause')); ?>" required>
                                                <?php $__errorArgs = ['disease_cause'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>

                                        <!-- Disease Symptoms -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_symptoms" class="form-label fw-semibold fs-6 required">Disease Symptoms</label>
                                                <input type="text" name="disease_symptoms" id="disease_symptoms" class="form-control form-control-solid <?php $__errorArgs = ['disease_symptoms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter disease symptoms" value="<?php echo e(old('disease_symptoms')); ?>" required>
                                                <?php $__errorArgs = ['disease_symptoms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Enter the main symptoms of the disease</div>
                                            </div>
                                        </div>

                                        <!-- Disease Treatment -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_treatment" class="form-label fw-semibold fs-6 required">Disease Treatment</label>
                                                <input type="text" name="disease_treatment" id="disease_treatment" class="form-control form-control-solid <?php $__errorArgs = ['disease_treatment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter disease treatment" value="<?php echo e(old('disease_treatment')); ?>" required>
                                                <?php $__errorArgs = ['disease_treatment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Enter the treatment methods for the disease</div>
                                            </div>
                                        </div>

                                        <!-- Disease Prevention -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_prevention" class="form-label fw-semibold fs-6 required">Disease Prevention</label>
                                                <input type="text" name="disease_prevention" id="disease_prevention" class="form-control form-control-solid <?php $__errorArgs = ['disease_prevention'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter disease prevention methods" value="<?php echo e(old('disease_prevention')); ?>" required>
                                                <?php $__errorArgs = ['disease_prevention'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Enter prevention methods for the disease</div>
                                            </div>
                                        </div>

                                        <!-- Disease First Found -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_first_found" class="form-label fw-semibold fs-6">Disease First Found</label>
                                                <input type="text" name="disease_first_found" id="disease_first_found" class="form-control form-control-solid <?php $__errorArgs = ['disease_first_found'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter when disease was first found" value="<?php echo e(old('disease_first_found')); ?>">
                                                <?php $__errorArgs = ['disease_first_found'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Enter when or where the disease was first discovered</div>
                                            </div>
                                        </div>

                                        <!-- Disease Effect -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_effect" class="form-label fw-semibold fs-6">Disease Effect</label>
                                                <input type="text" name="disease_effect" id="disease_effect" class="form-control form-control-solid <?php $__errorArgs = ['disease_effect'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter disease effects" value="<?php echo e(old('disease_effect')); ?>">
                                                <?php $__errorArgs = ['disease_effect'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Enter the effects of the disease on the body</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" <?php echo e(old('status') == '1' ? 'selected' : ''); ?>>Active</option>
                                                    <option value="0" <?php echo e(old('status') == '0' ? 'selected' : ''); ?>>Inactive</option>
                                                </select>
                                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Set the status for this disease</div>
                                            </div>
                                        </div>

                                        <!-- Disease Description -->
                                        <div class="col-md-12">
                                            <div class="fv-row">
                                                <label for="disease_description" class="form-label fw-semibold fs-6">Disease Description</label>
                                                <textarea name="disease_description" id="disease_description" class="form-control form-control-solid ckeditor <?php $__errorArgs = ['disease_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter detailed disease description" rows="4"><?php echo e(old('disease_description')); ?></textarea>
                                                <?php $__errorArgs = ['disease_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Enter a detailed description of the disease</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Save Disease</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="<?php echo e(route('diseases.index')); ?>" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'disease-create-form',
                submitBtnId: 'submit-btn',
                exitSelector: 'a[href="<?php echo e(route("diseases.index")); ?>"]',
                successMessage: 'Disease has been created successfully.',
                redirectUrl: '<?php echo e(route("diseases.index")); ?>',
                hasFileUpload: false
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/Web/diseases/create.blade.php ENDPATH**/ ?>